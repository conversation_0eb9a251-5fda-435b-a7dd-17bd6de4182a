name: Cross-Platform Testing

on:
  push:
    branches: [ main ]
    paths:
      - 'backend_api/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'backend_api/**'
  schedule:
    # Run weekly on Sundays at 2 AM UTC
    - cron: '0 2 * * 0'

jobs:
  cross-platform-test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.11', '3.12']
        include:
          # Add specific configurations for different OS
          - os: ubuntu-latest
            pip-cache-path: ~/.cache/pip
          - os: windows-latest
            pip-cache-path: ~\AppData\Local\pip\Cache
          - os: macos-latest
            pip-cache-path: ~/Library/Caches/pip
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ${{ matrix.pip-cache-path }}
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('backend_api/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-
          ${{ runner.os }}-pip-
    
    - name: Install dependencies (Unix)
      if: runner.os != 'Windows'
      run: |
        cd backend_api
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov
    
    - name: Install dependencies (Windows)
      if: runner.os == 'Windows'
      run: |
        cd backend_api
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov
      shell: cmd
    
    - name: Set up environment variables (Unix)
      if: runner.os != 'Windows'
      run: |
        cd backend_api
        cp .env.example .env
        echo "SUPABASE_URL=https://test.supabase.co" >> .env
        echo "SUPABASE_KEY=test_anon_key" >> .env
        echo "SUPABASE_SERVICE_KEY=test_service_key" >> .env
        echo "VERTEX_AI_PROJECT=test-project" >> .env
        echo "VERTEX_AI_LOCATION=us-central1" >> .env
        echo "STRIPE_SECRET_KEY=sk_test_123" >> .env
        echo "STRIPE_WEBHOOK_SECRET=whsec_test_123" >> .env
        echo "JWT_SECRET_KEY=test_jwt_secret" >> .env
        echo "ENVIRONMENT=test" >> .env
    
    - name: Set up environment variables (Windows)
      if: runner.os == 'Windows'
      run: |
        cd backend_api
        copy .env.example .env
        echo SUPABASE_URL=https://test.supabase.co >> .env
        echo SUPABASE_KEY=test_anon_key >> .env
        echo SUPABASE_SERVICE_KEY=test_service_key >> .env
        echo VERTEX_AI_PROJECT=test-project >> .env
        echo VERTEX_AI_LOCATION=us-central1 >> .env
        echo STRIPE_SECRET_KEY=sk_test_123 >> .env
        echo STRIPE_WEBHOOK_SECRET=whsec_test_123 >> .env
        echo JWT_SECRET_KEY=test_jwt_secret >> .env
        echo ENVIRONMENT=test >> .env
      shell: cmd
    
    - name: Run unit tests
      run: |
        cd backend_api
        pytest tests/unit/ -v --tb=short
    
    - name: Run integration tests
      run: |
        cd backend_api
        pytest tests/integration/ -v --tb=short
    
    - name: Test import statements
      run: |
        cd backend_api
        python -c "
        try:
            from app.main import app
            from app.services.supabase_client import get_supabase_client
            from app.services.vertex_ai import initialize_gemini_model
            from app.tools.tradingview_provider import get_price_data
            print('✅ All imports successful on ${{ matrix.os }} with Python ${{ matrix.python-version }}')
        except ImportError as e:
            print(f'❌ Import error on ${{ matrix.os }} with Python ${{ matrix.python-version }}: {e}')
            exit(1)
        "
    
    - name: Platform-specific tests
      run: |
        cd backend_api
        python -c "
        import platform
        import sys
        print(f'Platform: {platform.platform()}')
        print(f'Python version: {sys.version}')
        print(f'Python executable: {sys.executable}')
        
        # Test path handling
        import os
        from pathlib import Path
        
        # Test that our app can handle different path separators
        app_path = Path('app')
        models_path = app_path / 'models'
        print(f'App path: {app_path}')
        print(f'Models path: {models_path}')
        
        # Test environment variable handling
        os.environ['TEST_VAR'] = 'test_value'
        assert os.getenv('TEST_VAR') == 'test_value'
        print('✅ Environment variable handling works')
        
        print('✅ Platform-specific tests passed')
        "

  performance-test:
    runs-on: ubuntu-latest
    needs: cross-platform-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        cd backend_api
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark
    
    - name: Run performance tests
      run: |
        cd backend_api
        python -c "
        import time
        import sys
        sys.path.append('.')
        
        # Test import performance
        start_time = time.time()
        from app.main import app
        import_time = time.time() - start_time
        
        print(f'App import time: {import_time:.3f} seconds')
        
        if import_time > 5.0:
            print('⚠️ App import is slow (>5s)')
        else:
            print('✅ App import performance is good')
        
        # Test basic endpoint performance
        from fastapi.testclient import TestClient
        client = TestClient(app)
        
        start_time = time.time()
        response = client.get('/health')
        response_time = time.time() - start_time
        
        print(f'Health endpoint response time: {response_time:.3f} seconds')
        
        if response_time > 1.0:
            print('⚠️ Health endpoint is slow (>1s)')
        else:
            print('✅ Health endpoint performance is good')
        
        assert response.status_code == 200
        print('✅ Performance tests completed')
        "

  compatibility-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Check Python version compatibility
      run: |
        cd backend_api
        python -c "
        import ast
        import sys
        from pathlib import Path
        
        def check_python_syntax(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    source = f.read()
                ast.parse(source)
                return True
            except SyntaxError as e:
                print(f'Syntax error in {file_path}: {e}')
                return False
        
        # Check all Python files
        app_dir = Path('app')
        python_files = list(app_dir.rglob('*.py'))
        
        all_valid = True
        for py_file in python_files:
            if not check_python_syntax(py_file):
                all_valid = False
        
        if all_valid:
            print(f'✅ All {len(python_files)} Python files have valid syntax')
        else:
            print('❌ Some Python files have syntax errors')
            sys.exit(1)
        "
    
    - name: Check requirements compatibility
      run: |
        cd backend_api
        pip install pip-tools
        pip-compile --dry-run requirements.txt
        echo "✅ Requirements are compatible"
