# Plan de Trabajo: Desarrollo del Asistente Financiero IA

**Versión:** 4.0
**Fecha:** 11 de agosto de 2025
**Estado:** Fase 1 ✅ COMPLETADA | Fase 2 ✅ COMPLETADA | Base de Datos ✅ CONFIGURADA

Este documento establece el plan de desarrollo por fases para la aplicación "Asistente Financiero IA". El plan ha sido actualizado para alinearse con la arquitectura de backend unificado y para integrar las mejores prácticas de desarrollo y despliegue.

---

## Herramientas a Implementar en el Módulo de Herramientas (Integrado en el Backend)

El módulo de herramientas será el corazón analítico de nuestra aplicación, integrado dentro del Backend Unificado. Expondrá las siguientes funciones para que sean utilizadas por el modelo de IA:

1.  **`get_price_data`** ✅ **IMPLEMENTADO**:
    *   **Función:** Obtiene datos históricos de precios (OHLCV) para cualquier activo.
    *   **Uso para la IA:** Base para cualquier análisis gráfico. _"Muéstrame el gráfico diario de Tesla de los últimos 6 meses."_
    *   **Estado:** Completamente funcional con soporte para múltiples exchanges e intervalos

2.  **`apply_indicator`** ✅ **IMPLEMENTADO**:
    *   **Función:** Calcula y devuelve los valores de un indicador técnico específico (ej. RSI, MACD, Bandas de Bollinger).
    *   **Uso para la IA:** Permite análisis técnico avanzado. _"Calcula el RSI(14) para Bitcoin."_
    *   **Estado:** Soporta RSI, MACD, SMA, EMA, BBANDS, STOCH, ATR, ADX

3.  **`run_market_screener`** 🔄 **PENDIENTE FASE 3**:
    *   **Función:** Filtra el mercado en busca de activos que cumplan un conjunto de criterios.
    *   **Uso para la IA:** Descubrimiento de oportunidades. _"Encuéntrame acciones del sector tecnológico con un PER por debajo de 20."_
    *   **Estado:** Planificado para Fase 3

4.  **`get_market_movers`** 🔄 **PENDIENTE FASE 3**:
    *   **Función:** Busca los activos con mayores ganancias ("Top Gainers") y pérdidas ("Top Losers").
    *   **Uso para la IA:** Alimentar los widgets del dashboard. _"Dame las 5 acciones del NASDAQ que más han subido hoy."_
    *   **Estado:** Planificado para Fase 3

5.  **`get_fundamental_data`** 🔄 **PENDIENTE FASE 3**:
    *   **Función:** Obtiene datos financieros clave de una empresa (ingresos, capitalización de mercado, etc.).
    *   **Uso para la IA:** Proporciona contexto fundamental. _"¿Cuál es la capitalización de mercado actual de Microsoft?"_
    *   **Estado:** Planificado para Fase 3

6.  **`get_latest_news`** 🔄 **PENDIENTE FASE 3**:
    *   **Función:** Recupera los titulares de noticias más recientes relacionados con un activo.
    *   **Uso para la IA:** Análisis de sentimiento y contexto de eventos. _"¿Qué noticias recientes hay sobre el petróleo?"_
    *   **Estado:** Planificado para Fase 3

---

## Plan de Desarrollo por Fases

### Fase 1: Fundación del Backend Unificado e Infraestructura ✅ **COMPLETADA**

*   **Objetivo Principal:** Construir la columna vertebral de la aplicación: un backend robusto con autenticación, conexión a la IA y las herramientas iniciales.
*   **Entregables Clave:** ✅ **TODOS COMPLETADOS**
    *   ✅ Backend Unificado (FastAPI) funcional.
    *   ✅ Integración con Supabase para autenticación y gestión de usuarios.
    *   ✅ Integración con el modelo Gemini de Vertex AI.
    *   ✅ Implementación de las herramientas `get_price_data` y `apply_indicator` dentro del backend.
    *   ✅ **Pruebas unitarias y de integración** que cubran la lógica de negocio y las herramientas.
    *   ✅ **Configuración inicial del pipeline de CI (Integración Continua)** en GitHub Actions para ejecutar pruebas automáticamente.
*   **Archivos y Funciones Implementados:** ✅ **TODOS COMPLETADOS**
    *   ✅ Estructura completa de `backend_api/` con configuración moderna (pyproject.toml).
    *   ✅ `backend_api/services/supabase_client.py`: Lógica para crear usuarios, iniciar sesión y validar tokens.
    *   ✅ `backend_api/services/vertex_ai.py`: Lógica de conexión con Gemini y function calling.
    *   ✅ `backend_api/tools/tradingview_provider.py`: Lógica para `get_price_data` y `apply_indicator`.
    *   ✅ `backend_api/routes/chat.py`: Endpoint de chat completo, protegido por autenticación.
    *   ✅ `tests/` carpeta con pruebas unitarias y de integración completas para el backend.
    *   ✅ `.github/workflows/ci.yml`: Pipeline avanzado de CI con pruebas multiplataforma.

**Funcionalidades Adicionales Implementadas:**
*   ✅ Configuración centralizada con `app/config.py`
*   ✅ Modelos Pydantic completos en `app/models/chat.py`
*   ✅ Persistencia automática del historial de chat
*   ✅ Manejo robusto de errores y logging
*   ✅ Análisis de seguridad automatizado (Bandit, Safety)
*   ✅ Cobertura de código del 80%+
*   ✅ Documentación automática con FastAPI

### Fase 2: Frontend y Experiencia de Usuario ✅ **COMPLETADA**

*   **Objetivo Principal:** Desarrollar una Single-Page Application (SPA) completa en React que permita a los usuarios registrarse, autenticarse e interactuar con el Asistente Financiero IA a través de una interfaz intuitiva y moderna.
*   **Prerequisitos:** ✅ Fase 1 completada - Backend totalmente funcional
*   **Entregables Clave:** ✅ **TODOS COMPLETADOS**
    *   ✅ Frontend con flujos de registro, inicio de sesión y cierre de sesión funcionales.
    *   ✅ Dashboard con componentes de Chat, Visualización de Gráficos e Historial.
    *   ✅ Conexión completa frontend-backend con TanStack Query.
    *   ✅ **Implementación del historial de chat** (UI y almacenamiento en base de datos).
    *   ✅ **Actualizaciones optimistas** en el chat para mejor UX.
    *   ✅ **Gráficos financieros interactivos** con Lightweight Charts.
    *   ✅ **Responsive design** para desktop y móvil.
    *   ✅ **Testing unitario e integración** configurado con Vitest.
*   **Archivos Implementados:** ✅ **COMPLETADOS**
    *   ✅ `frontend/src/pages/AuthPage.tsx`, `DashboardPage.tsx`, `NotFoundPage.tsx`.
    *   ✅ `frontend/src/components/auth/` - LoginForm, RegisterForm, AuthLayout.
    *   ✅ `frontend/src/components/chat/` - ChatWindow, MessageBubble, ChatInput, TypingIndicator.
    *   ✅ `frontend/src/components/dashboard/` - DashboardLayout, MainChart, ChatHistoryPanel.
    *   ✅ `frontend/src/components/ui/` - Button, Input, Card, Spinner, ErrorBoundary.
    *   ✅ `frontend/src/lib/supabaseClient.ts`, `apiClient.ts`.
    *   ✅ `frontend/src/hooks/useAuth.ts`, `useChat.ts`, `useChatHistory.ts`, `useChartData.ts`.
    *   ✅ `frontend/src/store/` - authStore, chatStore con Zustand.
    *   ✅ `frontend/src/api/` - Cliente API con interceptores automáticos.
    *   ✅ Configuración completa con Vite, TypeScript, Tailwind CSS.
    *   ✅ Testing setup con Vitest + Testing Library + Playwright.

### Fase 2.5: Configuración de Base de Datos Supabase ✅ **COMPLETADA**

*   **Objetivo Principal:** Configurar la infraestructura de base de datos necesaria para soportar las funcionalidades actuales y preparar la base para la monetización futura.
*   **Fecha de Implementación:** 11 de agosto de 2025
*   **Prerequisitos:** ✅ Fases 1 y 2 completadas
*   **Entregables Clave:** ✅ **TODOS COMPLETADOS**
    *   ✅ **Proyecto Supabase Dedicado:** Nuevo proyecto "TradingIA" creado específicamente para la aplicación
    *   ✅ **Tabla chat_history:** Estructura completa para almacenar historial de conversaciones
    *   ✅ **Tabla profiles:** Preparación para gestión de perfiles y futura integración Stripe
    *   ✅ **Row Level Security (RLS):** Políticas de seguridad implementadas en todas las tablas
    *   ✅ **Trigger de Sincronización:** Creación automática de perfiles al registrar usuarios
    *   ✅ **Validación Completa:** Todas las funcionalidades probadas y verificadas

**Detalles de Implementación:**
*   **Proyecto Supabase:**
    *   **ID:** `dascysqiitlijhnfydjz`
    *   **Nombre:** TradingIA
    *   **Región:** eu-west-1
    *   **URL:** `https://dascysqiitlijhnfydjz.supabase.co`
    *   **Estado:** ACTIVE_HEALTHY

*   **Estructura de Base de Datos:**
    *   **auth.users:** ✅ Tabla de autenticación (Supabase Auth)
    *   **public.chat_history:** ✅ 7 columnas, 3 políticas RLS
    *   **public.profiles:** ✅ 5 columnas, 2 políticas RLS
    *   **Trigger:** ✅ `on_auth_user_created` para sincronización automática

*   **Seguridad Implementada:**
    *   **RLS Habilitado:** ✅ En todas las tablas sensibles
    *   **Políticas Totales:** ✅ 5 políticas de seguridad activas
    *   **Aislamiento de Datos:** ✅ Cada usuario solo accede a sus propios datos
    *   **Integridad Referencial:** ✅ Foreign keys con CASCADE para limpieza automática

### Fase 3: Monetización y Expansión de Funcionalidades

*   **Objetivo Principal:** Implementar el modelo de negocio con suscripciones y enriquecer la aplicación con el resto de las herramientas de análisis.
*   **Entregables Clave:**
    *   Integración con Stripe para la gestión de suscripciones recurrentes.
    *   Flujo completo de selección de plan, pago y actualización de rol de usuario.
    *   Implementación de las herramientas restantes (`run_market_screener`, `get_market_movers`, etc.) en el backend.
    *   Paneles de información en el Dashboard (Top Movers, etc.).
    *   **Pruebas E2E para el flujo de suscripción**.
*   **Archivos y Funciones a Implementar:**
    *   `backend_api/routes/payments.py`: Endpoint para webhooks de Stripe.
    *   `frontend/src/pages/PricingPage.tsx`, `AccountPage.tsx`.
    *   `frontend/src/services/stripe.ts`: Lógica para redirigir al checkout de Stripe.
    *   Nuevas funciones en `backend_api/tools/tradingview_provider.py`.
    *   Nuevos componentes en `frontend/` para los paneles de datos.

### Fase 4: Despliegue, Optimización y Lanzamiento

*   **Objetivo Principal:** Lanzar la aplicación al público utilizando las mejores herramientas para cada componente (Vercel para Frontend, Docker para Backend) y asegurar su estabilidad, seguridad y rendimiento.
*   **Entregables Clave:**
    *   **Frontend Desplegado en Vercel:** Conexión del repositorio de GitHub a Vercel para un despliegue continuo y automatizado.
    *   **Backend Desplegado en un Host de Contenedores:** El contenedor Docker del backend funcionando en un servicio como Google Cloud Run o Render.
    *   **Pipeline de CD para el Backend:** Configuración de GitHub Actions para que, tras un `push`, construya la imagen Docker del backend, la suba a un registro (ej. Google Artifact Registry) y la despliegue en el host final.
    *   Implementación de una capa de **caché con Redis** para optimizar el rendimiento y reducir costes.
    *   Sistema de logging y monitoreo configurado en la plataforma del backend.
*   **Tareas y Archivos a Implementar:**
    *   **Para el Frontend (Vercel):**
        1.  Crear un proyecto en la plataforma Vercel.
        2.  Enlazar el proyecto Vercel al repositorio de GitHub para habilitar el CI/CD automático.
        3.  Configurar las variables de entorno en el panel de Vercel (ej. `VITE_BACKEND_API_URL`, `VITE_SUPABASE_URL`).
    *   **Para el Backend (Docker en Google Cloud Run / Render):**
        1.  Finalizar y optimizar el `Dockerfile` en el directorio `backend_api/`.
        2.  Crear un proyecto en el host de contenedores elegido (ej. Google Cloud Run).
        3.  Configurar las variables de entorno y los secretos de forma segura en la plataforma del host (Stripe, Supabase, Vertex AI, Redis, etc.).
        4.  Crear el workflow de Despliegue Continuo en un archivo como `.github/workflows/deploy-backend.yml`.
    *   **Para la Infraestructura Adicional:**
        1.  Provisionar una instancia de Redis gestionada (ej. en Render, o Google Memorystore).
        2.  Configurar el sistema de logging y monitoreo que ofrece la plataforma del backend para supervisar el rendimiento y los errores.

---

### Consideraciones Transversales al Desarrollo

Estas prácticas deben ser observadas durante todas las fases:

*   **Testing Continuo:** Cada nueva funcionalidad en el backend debe ir acompañada de pruebas unitarias. Cada nuevo flujo de usuario debe ser cubierto por pruebas E2E.
*   **Seguridad Primero:** Las claves y secretos nunca deben estar en el código. Se deben usar variables de entorno localmente y un gestor de secretos para producción desde el inicio.
*   **Riesgo del Proveedor de Datos:** El módulo de herramientas (`tradingview_provider.py`) debe ser diseñado como una capa fácilmente reemplazable. El equipo debe ser consciente del riesgo de usar APIs no oficiales y estar preparado para migrar a un proveedor de pago si es necesario.

---

## 📊 Resumen del Progreso

### Estado Actual: Fases 1, 2 y Base de Datos Completadas ✅

**Fecha de Finalización Fase 1:** 8 de agosto de 2025
**Fecha de Finalización Fase 2:** 10 de agosto de 2025
**Fecha de Finalización Base de Datos:** 11 de agosto de 2025

**Logros Principales:**
- ✅ **Backend Unificado Completo:** FastAPI con arquitectura escalable
- ✅ **Frontend React Completo:** SPA con TypeScript, chat interactivo y gráficos
- ✅ **Base de Datos Configurada:** Supabase con tablas, RLS y triggers implementados
- ✅ **Autenticación Robusta:** Integración completa con Supabase Auth
- ✅ **IA Funcional:** Vertex AI con Gemini y function calling
- ✅ **Herramientas Financieras:** get_price_data y apply_indicator operativas
- ✅ **Testing Completo:** Cobertura 80%+ con pruebas unitarias e integración
- ✅ **CI/CD Avanzado:** GitHub Actions con pruebas multiplataforma
- ✅ **Calidad de Código:** Linting, formateo y análisis de seguridad

**Métricas de Calidad:**
- 📁 **Archivos Implementados:** 30+ archivos de código fuente (Backend + Frontend)
- 🧪 **Pruebas:** 80+ casos de prueba automatizados
- 🔒 **Seguridad:** Análisis automatizado + RLS en base de datos
- 🚀 **CI/CD:** Pruebas en Python 3.11/3.12 y Ubuntu/Windows/macOS
- 📊 **Cobertura:** >80% de cobertura de código
- 🗄️ **Base de Datos:** 2 tablas principales + 5 políticas de seguridad

**Infraestructura Actual:**
- **Proyecto Supabase:** TradingIA (`dascysqiitlijhnfydjz`)
- **Tablas Implementadas:** `chat_history`, `profiles`
- **Seguridad:** Row Level Security habilitado
- **Automatización:** Trigger para creación automática de perfiles

**Próximo Paso:** Iniciar Fase 3 - Monetización y Funcionalidades Avanzadas

**Tiempo Estimado Fase 3:** 3-4 semanas de desarrollo