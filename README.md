# TradingIA - Asistente Financiero IA

Una aplicación web completa que proporciona análisis financiero en tiempo real mediante IA conversacional. Los usuarios interactúan en lenguaje natural con un asistente que puede obtener datos de mercado, calcular indicadores técnicos y proporcionar análisis financiero.

## 🎯 Estado del Proyecto

**✅ Fase 1 Completada**: Backend Unificado con FastAPI + Vertex AI
**✅ Fase 2 Completada**: Frontend React con Chat Interactivo
**🔄 Fase 3 En Preparación**: Funcionalidades Avanzadas + Stripe

## 🏗️ Arquitectura

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: FastAPI + Python 3.11+
- **IA**: Google Cloud Vertex AI (Gemini 1.5 Pro) con Function Calling
- **Base de Datos**: Supabase (PostgreSQL)
- **Datos Financieros**: tvDataFeed (TradingView)
- **Autenticación**: Supabase JWT
- **Estado**: Zustand + TanStack Query
- **Gráficos**: Lightweight Charts
- **Testing**: Vitest + Testing Library + Playwright
- **Pagos**: Stripe (Fase 3)

## 📁 Estructura del Proyecto

```
/
├── backend_api/                    # Backend unificado (COMPLETADO ✅)
│   ├── app/
│   │   ├── main.py                # Punto de entrada FastAPI
│   │   ├── config.py              # Configuración centralizada
│   │   ├── models/chat.py         # Modelos Pydantic
│   │   ├── services/              # Servicios principales
│   │   │   ├── supabase_client.py # Autenticación y persistencia
│   │   │   └── vertex_ai.py       # Integración con Gemini
│   │   ├── tools/                 # Herramientas financieras
│   │   │   └── tradingview_provider.py
│   │   └── routes/chat.py         # Endpoints principales
│   ├── tests/                     # Pruebas completas (>80% cobertura)
│   ├── requirements.txt           # Dependencias Python
│   ├── .env.example              # Plantilla configuración
│   └── pyproject.toml            # Configuración moderna
├── frontend/                      # Frontend React (COMPLETADO ✅)
│   ├── src/
│   │   ├── components/            # Componentes React
│   │   │   ├── auth/             # Autenticación (Login/Register)
│   │   │   ├── chat/             # Chat interactivo
│   │   │   ├── dashboard/        # Dashboard y gráficos
│   │   │   └── ui/               # Componentes base
│   │   ├── pages/                # Páginas principales
│   │   ├── hooks/                # Hooks personalizados
│   │   ├── store/                # Estado global (Zustand)
│   │   ├── api/                  # Servicios API
│   │   ├── lib/                  # Configuraciones
│   │   ├── types/                # Tipos TypeScript
│   │   └── utils/                # Utilidades
│   ├── tests/                    # Testing completo
│   ├── package.json              # Dependencias Node.js
│   ├── tailwind.config.js        # Configuración Tailwind
│   ├── vite.config.ts            # Configuración Vite
│   └── .env.example              # Variables de entorno
└── .github/workflows/             # CI/CD automatizado
```

## 🚀 Guía de Desarrollo

### Configuración Inicial

#### 1. Clonar el Repositorio
```bash
git clone <repository-url>
cd TRADINGIA
```

#### 2. Configurar Backend (FastAPI)

```bash
cd backend_api

# Crear entorno virtual
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Instalar dependencias
pip install -r requirements.txt

# Configurar variables de entorno
cp .env.example .env
# Editar .env con valores reales:
# - SUPABASE_URL, SUPABASE_KEY
# - VERTEX_AI_PROJECT, VERTEX_AI_LOCATION
# - GOOGLE_APPLICATION_CREDENTIALS

# Ejecutar servidor
uvicorn app.main:app --reload
```

**Backend disponible en:** http://localhost:8000
**Documentación API:** http://localhost:8000/docs

#### 3. Configurar Frontend (React)

```bash
cd frontend

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env.local
# Editar .env.local con valores reales:
# - VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY
# - VITE_API_BASE_URL=http://localhost:8000

# Ejecutar servidor de desarrollo
npm run dev
```

**Frontend disponible en:** http://localhost:3000

### 🔧 Scripts Disponibles

#### Backend
```bash
# Desarrollo
uvicorn app.main:app --reload

# Testing
pytest
pytest --cov=app --cov-report=html

# Linting
bandit -r app/
safety check
```

#### Frontend
```bash
# Desarrollo
npm run dev

# Build de producción
npm run build

# Preview del build
npm run preview

# Testing
npm test                    # Tests unitarios
npm run test:coverage      # Con cobertura
npm run test:e2e          # Tests E2E
npm run test:e2e:ui       # Tests E2E con UI

# Linting
npm run lint
```

## 🎯 Funcionalidades Implementadas

### ✅ Backend (Fase 1)
- **API REST** con FastAPI y documentación automática
- **Autenticación JWT** con Supabase
- **IA Conversacional** con Gemini 1.5 Pro y Function Calling
- **Herramientas Financieras**: get_price_data, apply_indicator
- **Persistencia** de historial de chat en Supabase
- **Testing** completo con >80% cobertura
- **CI/CD** automatizado con GitHub Actions

### ✅ Frontend (Fase 2)
- **Autenticación completa** con formularios de login/registro
- **Chat interactivo** con IA en tiempo real
- **Interfaz moderna** con React 18 + TypeScript + Tailwind CSS
- **Gestión de estado** con Zustand + TanStack Query
- **Gráficos financieros** con Lightweight Charts
- **Historial de conversaciones** persistente
- **Actualizaciones optimistas** en el chat
- **Responsive design** para desktop y móvil
- **Testing** unitario e integración

### 🔄 Próximamente (Fase 3)
- Herramientas financieras adicionales
- Sistema de pagos con Stripe
- Dashboard avanzado con más visualizaciones
- Notificaciones en tiempo real
- PWA (Progressive Web App)

## 🛠️ Tecnologías Utilizadas

### Backend
- **FastAPI** - Framework web moderno y rápido
- **Python 3.11+** - Lenguaje de programación
- **Supabase** - Base de datos PostgreSQL y autenticación
- **Google Cloud Vertex AI** - IA y ML
- **tvDataFeed** - Datos financieros de TradingView
- **Pytest** - Framework de testing
- **Pydantic** - Validación de datos

### Frontend
- **React 18** - Librería de UI
- **TypeScript** - Tipado estático
- **Vite** - Build tool y dev server
- **Tailwind CSS** - Framework de estilos
- **Zustand** - Gestión de estado global
- **TanStack Query** - Estado del servidor
- **React Router** - Enrutamiento
- **React Hook Form** - Gestión de formularios
- **Zod** - Validación de esquemas
- **Lightweight Charts** - Gráficos financieros
- **React Markdown** - Renderizado de markdown
- **Vitest** - Testing framework
- **Playwright** - Testing E2E

## 🔐 Configuración de Seguridad

### Variables de Entorno Requeridas

#### Backend (.env)
```bash
# Supabase
SUPABASE_URL=https://tu-proyecto.supabase.co
SUPABASE_KEY=tu_anon_key
SUPABASE_SERVICE_KEY=tu_service_key

# Google Cloud Vertex AI
VERTEX_AI_PROJECT=tu-proyecto-gcp
VERTEX_AI_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Aplicación
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000
```

#### Frontend (.env.local)
```bash
# Supabase
VITE_SUPABASE_URL=https://tu-proyecto.supabase.co
VITE_SUPABASE_ANON_KEY=tu_anon_key

# Backend API
VITE_API_BASE_URL=http://localhost:8000

# Entorno
VITE_ENVIRONMENT=development
```

## 📚 Documentación Adicional

- **[Plan de Trabajo](Plan%20de%20trabajo%20TradingIA.md)** - Roadmap detallado del proyecto
- **[Especificaciones](Proyecto%20TradingIA.md)** - Especificaciones técnicas completas
- **API Docs** - http://localhost:8000/docs (cuando el backend esté ejecutándose)

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📞 Soporte

Para soporte técnico o preguntas sobre el proyecto, consulta la documentación o abre un issue en el repositorio.

## 📄 Licencia

Todos los derechos reservados.
