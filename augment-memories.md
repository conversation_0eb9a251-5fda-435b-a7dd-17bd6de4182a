# TradingIA - Augment Memories
## Información Esencial para Desarrolladores

**Proyecto:** TradingIA - Asistente Financiero IA  
**Estado:** Fase 1 Completada ✅ | Fase 2 En Preparación 🔄  
**Fecha Actualización:** 8 de agosto de 2025  
**Versión Backend:** 1.0.0  

---

## 🎯 Resumen del Proyecto

**TradingIA** es una aplicación web que proporciona análisis financiero en tiempo real mediante IA conversacional. Los usuarios interactúan en lenguaje natural con un asistente que puede obtener datos de mercado, calcular indicadores técnicos y proporcionar análisis financiero.

### Arquitectura Principal
- **Backend Unificado:** FastAPI con Python 3.11+
- **IA:** Google Cloud Vertex AI (Gemini 1.5 Pro) con function calling
- **Base de Datos:** Supabase (PostgreSQL)
- **Datos Financieros:** tvDataFeed (TradingView)
- **Autenticación:** Supabase JWT
- **Pagos:** Stripe (Fase 3)
- **Frontend:** React + TypeScript (Fase 2)

---

## 🏗️ Estructura del Proyecto

```
/
├── backend_api/                    # Backend unificado (COMPLETADO)
│   ├── app/
│   │   ├── main.py                # Punto de entrada FastAPI
│   │   ├── config.py              # Configuración centralizada
│   │   ├── models/chat.py         # Modelos Pydantic
│   │   ├── services/              # Servicios principales
│   │   │   ├── supabase_client.py # Autenticación y persistencia
│   │   │   └── vertex_ai.py       # Integración con Gemini
│   │   ├── tools/                 # Herramientas financieras
│   │   │   └── tradingview_provider.py
│   │   └── routes/chat.py         # Endpoints principales
│   ├── tests/                     # Pruebas completas (>80% cobertura)
│   ├── requirements.txt           # Dependencias Python
│   ├── .env.example              # Plantilla configuración
│   └── pyproject.toml            # Configuración moderna
├── frontend/                      # Frontend React (PENDIENTE FASE 2)
├── .github/workflows/             # CI/CD automatizado
└── documentación/                 # Planes y especificaciones
```

---

## 🔧 Configuración de Desarrollo

### Variables de Entorno Requeridas
```bash
# Supabase
SUPABASE_URL=https://tu-proyecto.supabase.co
SUPABASE_KEY=tu_anon_key
SUPABASE_SERVICE_KEY=tu_service_key

# Google Cloud Vertex AI
VERTEX_AI_PROJECT=tu-proyecto-gcp
VERTEX_AI_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Stripe (Fase 3)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Aplicación
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### Setup Rápido
```bash
# 1. Clonar y navegar
cd backend_api

# 2. Crear entorno virtual
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 3. Instalar dependencias
pip install -r requirements.txt

# 4. Configurar variables de entorno
cp .env.example .env
# Editar .env con valores reales

# 5. Ejecutar servidor
uvicorn app.main:app --reload
```

---

## 🤖 Integración con IA

### Modelo: Gemini 1.5 Pro
- **Function Calling:** Completamente implementado
- **Herramientas Disponibles:** get_price_data, apply_indicator
- **Instrucciones de Sistema:** Configuradas para comportamiento seguro
- **Límite de Iteraciones:** 3 tool calls máximo por conversación

### Flujo de Conversación
1. Usuario envía mensaje → Validación JWT
2. Mensaje → Gemini con herramientas disponibles
3. Si Gemini solicita herramienta → Ejecutar función
4. Resultado → Gemini para respuesta final
5. Respuesta + Historial → Guardar en Supabase

### Herramientas Implementadas
```python
# get_price_data: Datos OHLCV
get_price_data(
    symbol="NASDAQ:TSLA",    # Exchange:Symbol
    interval="1D",           # 1m,5m,15m,30m,1h,4h,1D,1W,1M
    n_bars=100              # Máximo 5000
)

# apply_indicator: Indicadores técnicos
apply_indicator(
    symbol="NASDAQ:TSLA",
    interval="1D", 
    indicator_name="RSI",    # RSI,MACD,SMA,EMA,BBANDS,STOCH,ATR,ADX
    parameters={"length": 14}
)
```

---

## 🔐 Autenticación y Seguridad

### Sistema de Autenticación
- **Proveedor:** Supabase Auth
- **Método:** JWT Bearer tokens
- **Validación:** Middleware en cada endpoint protegido
- **Persistencia:** Automática del historial por user_id

### Endpoints Principales
```python
# Público
GET  /health                    # Estado del servidor
GET  /docs                      # Documentación automática

# Protegido (requiere JWT)
POST /api/v1/chat/             # Chat principal
GET  /api/v1/chat/health       # Estado del servicio chat
```

### Flujo de Autenticación
1. Frontend → Login en Supabase → JWT token
2. Cada request → Header: `Authorization: Bearer <token>`
3. Backend → Validar token con Supabase
4. Si válido → Procesar request + Guardar historial

---

## 📊 Base de Datos (Supabase)

### Tablas Principales
```sql
-- Usuarios (gestionado por Supabase Auth)
auth.users (
    id uuid PRIMARY KEY,
    email text,
    created_at timestamp,
    user_metadata jsonb
)

-- Historial de Chat
chat_history (
    id uuid PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id),
    request_messages jsonb,
    ai_response text,
    conversation_id text,
    created_at timestamp,
    updated_at timestamp
)
```

### Funciones Implementadas
- `save_chat_history()`: Guardar conversaciones
- `get_user_chat_history()`: Recuperar historial
- `validate_user_token()`: Validar JWT

---

## 🧪 Testing y Calidad

### Cobertura de Pruebas: >80%
```bash
# Ejecutar todas las pruebas
pytest

# Solo unitarias
pytest tests/unit/

# Solo integración
pytest tests/integration/

# Con cobertura
pytest --cov=app --cov-report=html
```

### Tipos de Pruebas
- **Unitarias:** test_tools.py, test_services.py
- **Integración:** test_chat_route.py
- **Fixtures:** conftest.py con datos de prueba

### CI/CD
- **GitHub Actions:** Pruebas automáticas en push/PR
- **Multiplataforma:** Ubuntu, Windows, macOS
- **Python:** 3.11, 3.12
- **Análisis:** Bandit (seguridad), Safety (dependencias)

---

## 📈 Herramientas Financieras

### Estado de Implementación
✅ **get_price_data** - Datos históricos OHLCV
✅ **apply_indicator** - 8 indicadores técnicos
🔄 **run_market_screener** - Pendiente Fase 3
🔄 **get_market_movers** - Pendiente Fase 3
🔄 **get_fundamental_data** - Pendiente Fase 3
🔄 **get_latest_news** - Pendiente Fase 3

### Proveedor de Datos
- **Actual:** tvDataFeed (TradingView no oficial)
- **Arquitectura:** Capa de abstracción fácilmente reemplazable
- **Riesgo:** API no oficial, preparado para migración

---

## 🚀 Despliegue y DevOps

### Entornos
- **Desarrollo:** Local con uvicorn --reload
- **Testing:** GitHub Actions con pytest
- **Producción:** Pendiente (Vercel/Railway recomendado)

### Configuración de Producción
```python
# Cambios necesarios para producción
ENVIRONMENT=production
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=https://tu-dominio.com
```

### Monitoreo
- **Logs:** Configurados con logging estándar
- **Health Checks:** /health y /api/v1/chat/health
- **Métricas:** Pendiente implementación

---

## 🔄 Roadmap de Desarrollo

### Fase 1: Backend Unificado ✅ COMPLETADA
- ✅ FastAPI con autenticación
- ✅ Integración Vertex AI + Gemini
- ✅ Herramientas financieras básicas
- ✅ Testing completo + CI/CD

### Fase 2: Frontend y UX 🔄 EN CURSO
- 🔄 React + TypeScript
- 🔄 Componentes de autenticación
- 🔄 Chat interactivo
- 🔄 Visualización de datos

### Fase 3: Funcionalidades Avanzadas 📋 PLANIFICADA
- 📋 Herramientas financieras adicionales
- 📋 Sistema de pagos (Stripe)
- 📋 Dashboard avanzado
- 📋 Notificaciones en tiempo real

---

## ⚠️ Consideraciones Importantes

### Limitaciones Actuales
1. **Datos:** Dependencia de tvDataFeed (no oficial)
2. **Escalabilidad:** Sin caché implementado
3. **Monitoreo:** Métricas básicas solamente
4. **Rate Limiting:** No implementado

### Mejores Prácticas
1. **Siempre validar tokens JWT** en endpoints protegidos
2. **Usar configuración centralizada** (config.py)
3. **Manejar errores gracefully** con HTTPException
4. **Escribir pruebas** para nuevas funcionalidades
5. **Documentar cambios** en este archivo

### Debugging
```bash
# Logs detallados
uvicorn app.main:app --reload --log-level debug

# Verificar configuración
python -c "from app.config import settings; print(settings.dict())"

# Probar herramientas
python -c "from app.tools.tradingview_provider import get_price_data; print(get_price_data('NASDAQ:TSLA', '1D', 5))"
```

---

## 📞 Contacto y Recursos

### Documentación
- **API Docs:** http://localhost:8000/docs (cuando servidor activo)
- **Plan de Trabajo:** Plan de trabajo TradingIA.md
- **Especificaciones:** Proyecto TradingIA.md

### Recursos Externos
- **Supabase Docs:** https://supabase.com/docs
- **Vertex AI Docs:** https://cloud.google.com/vertex-ai/docs
- **FastAPI Docs:** https://fastapi.tiangolo.com
- **tvDataFeed:** https://github.com/StreamAlpha/tvdatafeed

---

**Última Actualización:** 8 de agosto de 2025  
**Mantenido por:** Equipo TradingIA  
**Versión:** 2.2
