# Supabase Configuration - PROYECTO DEDICADO TRADINGIA
# IMPORTANTE: Actualizar con las credenciales del nuevo proyecto TradingIA (dascysqiitlijhnfydjz)
SUPABASE_URL=https://dascysqiitlijhnfydjz.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRhc2N5c3FpaXRsaWpobmZ5ZGp6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ5MTYwNTEsImV4cCI6MjA3MDQ5MjA1MX0.kGEzGrPYyiU9uixuoA7vqhcVSM80o2SX92i4TOqtd7E
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRhc2N5c3FpaXRsaWpobmZ5ZGp6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDkxNjA1MSwiZXhwIjoyMDcwNDkyMDUxfQ.u2r6njx1Q_E-TIP4b3fRzwWLvqMICRem8cxFpIJtbg8

# Google Cloud Vertex AI Configuration
VERTEX_AI_PROJECT=tradingia-468810
VERTEX_AI_LOCATION=europe-west1
GOOGLE_APPLICATION_CREDENTIALS=./google-credentials.json


# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Application Configuration
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256

# Redis Configuration (for future caching)
REDIS_URL=redis://localhost:6379
