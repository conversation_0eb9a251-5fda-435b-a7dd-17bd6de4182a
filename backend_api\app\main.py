"""
Main FastAPI application entry point for TradingIA Backend.

This module initializes the FastAPI application with all necessary
middleware, CORS configuration, and route registration.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from app.config import settings

# Create FastAPI application instance
app = FastAPI(
    title="TradingIA Backend API",
    description="Backend unificado para el Asistente Financiero IA",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add trusted host middleware for security
allowed_hosts = ["localhost", "127.0.0.1", "*.vercel.app"]
# Add testserver for testing environment
if settings.environment == "test":
    allowed_hosts.append("testserver")

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=allowed_hosts
)


@app.get("/")
async def root():
    """Root endpoint for health check."""
    return {
        "message": "TradingIA Backend API",
        "status": "running",
        "version": "1.0.0"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "tradingIA-backend"
    }


# Register route modules
from app.routes import chat

app.include_router(chat.router, prefix="/api/v1")
# TODO: Add payments router when implemented
# app.include_router(payments.router, prefix="/api/v1")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,
        log_level="info"
    )
