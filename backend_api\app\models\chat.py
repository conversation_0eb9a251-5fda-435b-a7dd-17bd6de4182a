"""
Pydantic models for chat functionality.

This module defines the data schemas for chat requests, responses,
and message structures used throughout the TradingIA API.
"""

from pydantic import BaseModel, Field
from typing import List, Literal, Optional
from datetime import datetime


class ChatMessage(BaseModel):
    """
    Represents a single message in a chat conversation.
    
    This model is used to structure individual messages within
    the conversation history, supporting both user and assistant roles.
    """
    
    role: Literal["user", "assistant", "system"] = Field(
        ...,
        description="The role of the message sender",
        examples=["user", "assistant", "system"]
    )
    
    content: str = Field(
        ...,
        min_length=1,
        max_length=10000,
        description="The text content of the message",
        examples=["¿Cuál es el precio actual de Tesla?"]
    )
    
    timestamp: Optional[datetime] = Field(
        default=None,
        description="When the message was created"
    )
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "role": "user",
                "content": "¿Puedes mostrarme el gráfico de Apple de los últimos 30 días?",
                "timestamp": "2025-08-08T10:30:00Z"
            }
        }


class ChatRequest(BaseModel):
    """
    Represents a chat request from the client.

    This model defines the structure of the request body sent to the
    chat endpoint, containing the conversation history.
    """

    history: List[ChatMessage] = Field(
        default_factory=list,
        description="List of previous messages in the conversation",
        max_items=100
    )

    message: str = Field(
        ...,
        min_length=1,
        max_length=2000,
        description="The new message from the user",
        examples=["¿Cuál es el RSI actual de Bitcoin?"]
    )

    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "history": [
                    {
                        "role": "user",
                        "content": "Hola, ¿puedes ayudarme con análisis financiero?",
                        "timestamp": "2025-08-08T10:25:00Z"
                    },
                    {
                        "role": "assistant",
                        "content": "¡Por supuesto! Puedo ayudarte con análisis técnico, datos de precios, indicadores y más. ¿Qué te gustaría analizar?",
                        "timestamp": "2025-08-08T10:25:05Z"
                    }
                ],
                "message": "¿Puedes mostrarme el precio actual de Tesla y calcular su RSI?"
            }
        }


class ChatResponse(BaseModel):
    """
    Represents the response from the chat endpoint.

    This model defines the structure of the JSON response sent back
    to the client after processing a chat request.
    """

    reply: str = Field(
        ...,
        min_length=1,
        description="The AI-generated response to the user's message",
        examples=["El precio actual de Tesla (TSLA) es $248.50. El RSI(14) está en 65.2, indicando una condición neutral a ligeramente sobrecomprada."]
    )

    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="When the response was generated"
    )

    conversation_id: Optional[str] = Field(
        default=None,
        description="Unique identifier for the conversation session"
    )

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "reply": "El precio actual de Tesla (TSLA) es $248.50. Basándome en los datos más recientes, el RSI(14) está en 65.2, lo que indica una condición neutral a ligeramente sobrecomprada. ¿Te gustaría que analice algún otro indicador técnico?",
                "timestamp": "2025-08-08T10:30:15Z",
                "conversation_id": "conv_123456789"
            }
        }


class ErrorResponse(BaseModel):
    """
    Represents an error response from the API.

    This model is used to structure error messages in a consistent format.
    """

    error: str = Field(
        ...,
        description="Error message describing what went wrong"
    )

    error_code: str = Field(
        ...,
        description="Machine-readable error code"
    )

    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="When the error occurred"
    )

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "error": "Authentication failed. Please provide a valid token.",
                "error_code": "AUTH_INVALID_TOKEN",
                "timestamp": "2025-08-08T10:30:00Z"
            }
        }
