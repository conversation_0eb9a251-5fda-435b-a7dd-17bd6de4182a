"""
Modelos Pydantic para el sistema de scheduler
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class SchedulerStatusEnum(str, Enum):
    """Estados posibles del scheduler"""
    STOPPED = "stopped"
    RUNNING = "running"
    ERROR = "error"
    PAUSED = "paused"

class SchedulerConfig(BaseModel):
    """Configuración del scheduler"""
    enabled: bool = Field(default=True, description="Si el scheduler está habilitado")
    interval_minutes: int = Field(default=15, ge=1, le=60, description="Intervalo en minutos entre actualizaciones")
    market_hours_only: bool = Field(default=True, description="Solo ejecutar durante horas de mercado")
    market_open_hour: int = Field(default=9, ge=0, le=23, description="Hora de apertura del mercado")
    market_close_hour: int = Field(default=16, ge=0, le=23, description="Hora de cierre del mercado")
    timezone: str = Field(default="US/Eastern", description="Zona horaria del mercado")
    batch_size: int = Field(default=10, ge=1, le=50, description="Tamaño del lote para procesamiento")
    
    class Config:
        schema_extra = {
            "example": {
                "enabled": True,
                "interval_minutes": 15,
                "market_hours_only": True,
                "market_open_hour": 9,
                "market_close_hour": 16,
                "timezone": "US/Eastern",
                "batch_size": 10
            }
        }

class SchedulerStatus(BaseModel):
    """Estado actual del scheduler"""
    status: SchedulerStatusEnum = Field(description="Estado actual del scheduler")
    last_run: Optional[datetime] = Field(default=None, description="Última ejecución")
    next_run: Optional[datetime] = Field(default=None, description="Próxima ejecución programada")
    total_runs: int = Field(default=0, ge=0, description="Total de ejecuciones")
    error_count: int = Field(default=0, ge=0, description="Número de errores")
    config: SchedulerConfig = Field(description="Configuración actual")
    thread_alive: bool = Field(default=False, description="Si el thread está activo")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "running",
                "last_run": "2025-01-23T12:00:00",
                "next_run": "2025-01-23T12:15:00",
                "total_runs": 42,
                "error_count": 1,
                "config": {
                    "enabled": True,
                    "interval_minutes": 15,
                    "market_hours_only": True
                },
                "thread_alive": True
            }
        }

class SchedulerCommand(BaseModel):
    """Comando para el scheduler"""
    action: str = Field(description="Acción a ejecutar: start, stop, pause, resume")
    config: Optional[SchedulerConfig] = Field(default=None, description="Nueva configuración (opcional)")
    
    class Config:
        schema_extra = {
            "example": {
                "action": "start",
                "config": {
                    "interval_minutes": 30,
                    "market_hours_only": False
                }
            }
        }

class SchedulerResponse(BaseModel):
    """Respuesta de operaciones del scheduler"""
    status: str = Field(description="Estado de la operación")
    message: str = Field(description="Mensaje descriptivo")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Datos adicionales")
    timestamp: datetime = Field(description="Timestamp de la respuesta")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "success",
                "message": "Scheduler iniciado correctamente",
                "data": {
                    "scheduler_status": "running",
                    "next_run": "2025-01-23T12:15:00"
                },
                "timestamp": "2025-01-23T12:00:00"
            }
        }

class UpdateRequest(BaseModel):
    """Request para actualización manual de datos"""
    batch_size: Optional[int] = Field(default=10, ge=1, le=50, description="Tamaño del lote")
    specific_symbols: Optional[str] = Field(default=None, description="Símbolos específicos separados por coma")
    force_update: bool = Field(default=False, description="Forzar actualización aunque no sea horario de mercado")
    
    class Config:
        schema_extra = {
            "example": {
                "batch_size": 20,
                "specific_symbols": "NASDAQ:AAPL,NASDAQ:TSLA,BINANCE:BTCUSDT",
                "force_update": False
            }
        }

class UpdateResponse(BaseModel):
    """Respuesta de actualización de datos"""
    message: str = Field(description="Mensaje de resultado")
    result: Dict[str, Any] = Field(description="Resultado detallado de la actualización")
    parameters: Dict[str, Any] = Field(description="Parámetros utilizados")
    timestamp: datetime = Field(description="Timestamp de la operación")
    status: str = Field(description="Estado de la operación")
    
    class Config:
        schema_extra = {
            "example": {
                "message": "Actualización completada",
                "result": {
                    "updated_assets": 65,
                    "updated_indicators": 110,
                    "errors": 2
                },
                "parameters": {
                    "batch_size": 10,
                    "specific_symbols": None
                },
                "timestamp": "2025-01-23T12:00:00",
                "status": "success"
            }
        }
