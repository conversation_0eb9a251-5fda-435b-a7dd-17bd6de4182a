"""
Market Data Service - Gestiona datos de mercado en base de datos
"""

import asyncio
import logging
import math
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from app.tools.tradingview_provider import get_price_data, apply_indicator
from app.tools.volume_analysis import calculate_vwap, analyze_volume_confirmation
from app.tools.pattern_recognition import detect_chart_patterns
from app.tools.multi_timeframe import get_multi_timeframe_analysis
from app.tools.advanced_indicators import identify_support_resistance
from app.tools.market_structure import analyze_market_structure
from app.services.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)

def sanitize_json_value(value):
    """Sanitiza valores para que sean compatibles con JSON"""
    if value is None:
        return None
    if isinstance(value, (int, str, bool)):
        return value
    if isinstance(value, float):
        if math.isnan(value) or math.isinf(value):
            return None
        return value
    return str(value)

def sanitize_dict_for_json(data: Dict) -> Dict:
    """Sanitiza un diccionario completo para JSON"""
    if not isinstance(data, dict):
        return data

    sanitized = {}
    for key, value in data.items():
        if isinstance(value, dict):
            sanitized[key] = sanitize_dict_for_json(value)
        elif isinstance(value, list):
            sanitized[key] = [sanitize_json_value(item) for item in value]
        else:
            sanitized[key] = sanitize_json_value(value)
    return sanitized

# Activos populares para pre-cargar - LISTA EXPANDIDA
POPULAR_ASSETS = [
    # === TECNOLOGÍA (NASDAQ) ===
    {"symbol": "NASDAQ:AAPL", "name": "Apple Inc."},
    {"symbol": "NASDAQ:MSFT", "name": "Microsoft Corp."},
    {"symbol": "NASDAQ:GOOGL", "name": "Alphabet Inc."},
    {"symbol": "NASDAQ:AMZN", "name": "Amazon.com Inc."},
    {"symbol": "NASDAQ:NVDA", "name": "NVIDIA Corp."},
    {"symbol": "NASDAQ:TSLA", "name": "Tesla Inc."},
    {"symbol": "NASDAQ:META", "name": "Meta Platforms Inc."},
    {"symbol": "NASDAQ:NFLX", "name": "Netflix Inc."},
    {"symbol": "NASDAQ:ADBE", "name": "Adobe Inc."},
    {"symbol": "NASDAQ:CRM", "name": "Salesforce Inc."},
    {"symbol": "NASDAQ:ORCL", "name": "Oracle Corp."},
    {"symbol": "NASDAQ:INTC", "name": "Intel Corp."},
    {"symbol": "NASDAQ:AMD", "name": "Advanced Micro Devices"},
    {"symbol": "NASDAQ:PYPL", "name": "PayPal Holdings"},
    {"symbol": "NASDAQ:UBER", "name": "Uber Technologies"},
    # {"symbol": "NASDAQ:ZOOM", "name": "Zoom Video Communications"}, # DESLISTADO
    {"symbol": "NASDAQ:SPOT", "name": "Spotify Technology"},
    {"symbol": "NASDAQ:SHOP", "name": "Shopify Inc."},

    # === ÍNDICES PRINCIPALES ===
    {"symbol": "NASDAQ:QQQ", "name": "Invesco QQQ Trust"},
    {"symbol": "NYSE:SPY", "name": "SPDR S&P 500 ETF"},
    {"symbol": "NYSE:DIA", "name": "SPDR Dow Jones Industrial Average ETF"},
    {"symbol": "NYSE:IWM", "name": "iShares Russell 2000 ETF"},

    # === FINANZAS Y BANCOS ===
    {"symbol": "NYSE:JPM", "name": "JPMorgan Chase & Co."},
    {"symbol": "NYSE:BAC", "name": "Bank of America Corp."},
    {"symbol": "NYSE:WFC", "name": "Wells Fargo & Co."},
    {"symbol": "NYSE:GS", "name": "Goldman Sachs Group"},
    {"symbol": "NYSE:MS", "name": "Morgan Stanley"},
    {"symbol": "NYSE:V", "name": "Visa Inc."},
    {"symbol": "NYSE:MA", "name": "Mastercard Inc."},

    # === ENERGÍA Y MATERIALES ===
    {"symbol": "NYSE:XOM", "name": "Exxon Mobil Corp."},
    {"symbol": "NYSE:CVX", "name": "Chevron Corp."},
    {"symbol": "NYSE:COP", "name": "ConocoPhillips"},
    {"symbol": "NYSE:SLB", "name": "Schlumberger Ltd."},

    # === SALUD Y FARMACÉUTICAS ===
    {"symbol": "NYSE:JNJ", "name": "Johnson & Johnson"},
    {"symbol": "NYSE:PFE", "name": "Pfizer Inc."},
    {"symbol": "NASDAQ:MRNA", "name": "Moderna Inc."},
    {"symbol": "NYSE:UNH", "name": "UnitedHealth Group"},
    {"symbol": "NYSE:ABT", "name": "Abbott Laboratories"},

    # === CONSUMO Y RETAIL ===
    {"symbol": "NYSE:KO", "name": "Coca-Cola Co."},
    {"symbol": "NYSE:PEP", "name": "PepsiCo Inc."},
    {"symbol": "NYSE:WMT", "name": "Walmart Inc."},
    {"symbol": "NYSE:HD", "name": "Home Depot Inc."},
    {"symbol": "NYSE:MCD", "name": "McDonald's Corp."},
    {"symbol": "NYSE:NKE", "name": "Nike Inc."},
    {"symbol": "NYSE:DIS", "name": "Walt Disney Co."},

    # === CRIPTOMONEDAS PRINCIPALES ===
    {"symbol": "BINANCE:BTCUSDT", "name": "Bitcoin"},
    {"symbol": "BINANCE:ETHUSDT", "name": "Ethereum"},
    {"symbol": "BINANCE:BNBUSDT", "name": "Binance Coin"},
    {"symbol": "BINANCE:ADAUSDT", "name": "Cardano"},
    {"symbol": "BINANCE:SOLUSDT", "name": "Solana"},
    {"symbol": "BINANCE:XRPUSDT", "name": "XRP"},
    {"symbol": "BINANCE:DOTUSDT", "name": "Polkadot"},
    {"symbol": "BINANCE:AVAXUSDT", "name": "Avalanche"},
    {"symbol": "BINANCE:LINKUSDT", "name": "Chainlink"},
    # {"symbol": "BINANCE:POLUSDT", "name": "Polygon (POL)"},  # Deshabilitado: POL también tiene problemas
    # {"symbol": "BINANCE:UNIUSDT", "name": "Uniswap"},  # Deshabilitado: problemas de conectividad
    {"symbol": "BINANCE:LTCUSDT", "name": "Litecoin"},
    {"symbol": "BINANCE:BCHUSDT", "name": "Bitcoin Cash"},
    # {"symbol": "BINANCE:ETCUSDT", "name": "Ethereum Classic"},  # Temporalmente deshabilitado por problemas de conectividad
    {"symbol": "BINANCE:XLMUSDT", "name": "Stellar"},
    {"symbol": "BINANCE:VETUSDT", "name": "VeChain"},
    {"symbol": "BINANCE:TRXUSDT", "name": "TRON"},
    # {"symbol": "BINANCE:EOSUSDT", "name": "EOS"},  # Deshabilitado: posiblemente deslistado
    {"symbol": "BINANCE:XMRUSDT", "name": "Monero"},
    {"symbol": "BINANCE:DASHUSDT", "name": "Dash"},

    # === COMMODITIES Y METALES ===
    {"symbol": "COMEX:GC1!", "name": "Gold Futures"},
    {"symbol": "COMEX:SI1!", "name": "Silver Futures"},
    {"symbol": "NYMEX:CL1!", "name": "Crude Oil Futures"},
    {"symbol": "CBOT:ZC1!", "name": "Corn Futures"},
    {"symbol": "CBOT:ZW1!", "name": "Wheat Futures"},

    # === FOREX PRINCIPALES ===
    {"symbol": "FX:EURUSD", "name": "Euro/US Dollar"},
    {"symbol": "FX:GBPUSD", "name": "British Pound/US Dollar"},
    {"symbol": "FX:USDJPY", "name": "US Dollar/Japanese Yen"},
    {"symbol": "FX:USDCHF", "name": "US Dollar/Swiss Franc"},
    {"symbol": "FX:AUDUSD", "name": "Australian Dollar/US Dollar"},
    {"symbol": "FX:USDCAD", "name": "US Dollar/Canadian Dollar"},
]

# Indicadores técnicos a calcular - EXPANDIDO
TECHNICAL_INDICATORS = [
    "RSI",      # Índice de Fuerza Relativa
    "MACD",     # Convergencia/Divergencia de Medias Móviles
    "SMA",      # Media Móvil Simple
    "EMA",      # Media Móvil Exponencial
    "BBANDS",   # Bandas de Bollinger
    "STOCH",    # Oscilador Estocástico
    "ATR",      # Average True Range (volatilidad)
    "ADX"       # Average Directional Index (fuerza de tendencia)
]

class MarketDataService:
    """Servicio para gestionar datos de mercado en base de datos"""
    
    def __init__(self):
        self.supabase = get_supabase_client()
    
    async def update_market_data(self, batch_size: int = 10) -> Dict[str, Any]:
        """
        Actualiza datos de mercado para todos los activos populares
        Procesa en lotes para evitar sobrecarga
        """
        results = {
            "updated_assets": 0,
            "updated_indicators": 0,
            "errors": [],
            "total_assets": len(POPULAR_ASSETS)
        }

        logger.info(f"🔄 Iniciando actualización de {len(POPULAR_ASSETS)} activos en lotes de {batch_size}...")

        # Procesar en lotes para evitar sobrecarga
        for i in range(0, len(POPULAR_ASSETS), batch_size):
            batch = POPULAR_ASSETS[i:i + batch_size]
            logger.info(f"📦 Procesando lote {i//batch_size + 1}/{(len(POPULAR_ASSETS) + batch_size - 1)//batch_size}")

            for asset in batch:
                try:
                    # Obtener datos de precio con retry
                    price_data = await self._get_price_data_with_retry(asset["symbol"])
                    if price_data:
                        await self._save_market_data(asset["symbol"], asset["name"], price_data)
                        results["updated_assets"] += 1
                        logger.debug(f"✅ {asset['symbol']}: Precio actualizado")

                        # Calcular indicadores técnicos (solo para activos principales)
                        if self._is_main_asset(asset["symbol"]):
                            for indicator in TECHNICAL_INDICATORS:
                                try:
                                    indicator_data = await self._calculate_indicator(asset["symbol"], indicator)
                                    if indicator_data:
                                        await self._save_indicator_data(asset["symbol"], indicator, indicator_data)
                                        results["updated_indicators"] += 1
                                except Exception as e:
                                    logger.warning(f"Error calculando {indicator} para {asset['symbol']}: {e}")
                                    results["errors"].append(f"{asset['symbol']}-{indicator}: {str(e)}")
                    else:
                        logger.warning(f"⚠️ No se pudieron obtener datos para {asset['symbol']}")
                        results["errors"].append(f"{asset['symbol']}: No data available")

                except Exception as e:
                    logger.error(f"Error actualizando {asset['symbol']}: {e}")
                    results["errors"].append(f"{asset['symbol']}: {str(e)}")

            # Pequeña pausa entre lotes para no sobrecargar las APIs
            if i + batch_size < len(POPULAR_ASSETS):
                await asyncio.sleep(1)

        logger.info(f"✅ Actualización completada: {results}")
        return results

    async def update_specific_assets(self, symbols: List[str], batch_size: int = 10) -> Dict[str, Any]:
        """
        Actualiza solo los símbolos específicos proporcionados

        Args:
            symbols: Lista de símbolos a actualizar (ej: ['BINANCE:BTCUSDT', 'NASDAQ:AAPL'])
            batch_size: Tamaño del lote para procesamiento
        """
        try:
            logger.info(f"🎯 Iniciando actualización de símbolos específicos: {symbols}")

            results = {
                "updated_assets": 0,
                "updated_indicators": 0,
                "errors": [],
                "total_symbols": len(symbols),
                "processed_symbols": []
            }

            # Crear diccionario de activos por símbolo para búsqueda rápida
            assets_by_symbol = {asset["symbol"]: asset for asset in POPULAR_ASSETS}

            # Validar que los símbolos existen en nuestra lista
            valid_assets = []
            for symbol in symbols:
                if symbol in assets_by_symbol:
                    valid_assets.append(assets_by_symbol[symbol])
                    logger.info(f"✅ Símbolo válido: {symbol}")
                else:
                    logger.warning(f"⚠️ Símbolo no reconocido: {symbol}")
                    results["errors"].append(f"{symbol}: Símbolo no está en la lista de activos configurados")

            if not valid_assets:
                logger.warning("❌ No se encontraron símbolos válidos")
                return results

            logger.info(f"🎯 Procesando {len(valid_assets)} símbolos válidos")

            # Procesar símbolos válidos en lotes
            for i in range(0, len(valid_assets), batch_size):
                batch = valid_assets[i:i + batch_size]
                logger.info(f"📦 Procesando lote específico {i//batch_size + 1}: {[asset['symbol'] for asset in batch]}")

                for asset in batch:
                    try:
                        symbol = asset["symbol"]
                        logger.info(f"🔄 Actualizando {symbol}...")

                        # Obtener datos de precio con retry
                        price_data = await self._get_price_data_with_retry(symbol)
                        if price_data:
                            await self._save_market_data(symbol, asset["name"], price_data)
                            results["updated_assets"] += 1
                            results["processed_symbols"].append(symbol)
                            logger.info(f"✅ {symbol}: Precio actualizado")

                            # Calcular indicadores técnicos (solo para activos principales)
                            if self._is_main_asset(symbol):
                                for indicator in TECHNICAL_INDICATORS:
                                    try:
                                        indicator_data = await self._calculate_indicator(symbol, indicator)
                                        if indicator_data:
                                            await self._save_indicator_data(symbol, indicator, indicator_data)
                                            results["updated_indicators"] += 1
                                            logger.debug(f"✅ {symbol}: {indicator} calculado")
                                    except Exception as e:
                                        logger.warning(f"Error calculando {indicator} para {symbol}: {e}")
                                        results["errors"].append(f"{symbol}-{indicator}: {str(e)}")
                            else:
                                logger.debug(f"ℹ️ {symbol}: No es activo principal, saltando indicadores")
                        else:
                            logger.warning(f"⚠️ No se pudieron obtener datos para {symbol}")
                            results["errors"].append(f"{symbol}: No data available")

                        # Pequeña pausa para evitar rate limiting
                        await asyncio.sleep(0.2)

                    except Exception as e:
                        logger.error(f"❌ Error actualizando {asset['symbol']}: {e}")
                        results["errors"].append(f"{asset['symbol']}: {str(e)}")

                # Pausa entre lotes
                if i + batch_size < len(valid_assets):
                    logger.info(f"⏸️ Pausa entre lotes específicos...")
                    await asyncio.sleep(0.5)

            logger.info(f"🎯 Actualización específica completada: {results}")
            return results

        except Exception as e:
            logger.error(f"❌ Error en actualización específica: {e}")
            raise

    def _is_main_asset(self, symbol: str) -> bool:
        """Determina si un activo es principal para calcular indicadores"""
        main_assets = [
            "NASDAQ:AAPL", "NASDAQ:TSLA", "NASDAQ:MSFT", "NASDAQ:GOOGL",
            "NASDAQ:AMZN", "NASDAQ:NVDA", "NASDAQ:META", "NASDAQ:NFLX",
            "BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT",
            "BINANCE:ADAUSDT", "BINANCE:SOLUSDT", "BINANCE:XRPUSDT"
        ]
        return symbol in main_assets
    
    async def _get_asset_price_data(self, symbol: str) -> Optional[Dict]:
        """Obtiene datos de precio para un activo"""
        try:
            data = get_price_data(symbol, "1D", 5)
            if data and "latest_price" in data:
                return data
        except Exception as e:
            logger.error(f"Error obteniendo precio de {symbol}: {e}")
        return None

    async def _get_price_data_with_retry(self, symbol: str, retries: int = 3) -> Optional[Dict]:
        """Obtiene datos de precio con reintentos y backoff exponencial"""
        for attempt in range(retries + 1):
            try:
                data = get_price_data(symbol, "1D", 100)
                if data and data.get("data"):
                    return data
            except Exception as e:
                if attempt == retries:
                    logger.error(f"Error obteniendo precio de {symbol} después de {retries} reintentos: {e}")
                else:
                    wait_time = (2 ** attempt) + 1  # Backoff exponencial: 1s, 3s, 5s
                    logger.warning(f"Intento {attempt + 1} fallido para {symbol}: {e}. Reintentando en {wait_time}s...")
                    await asyncio.sleep(wait_time)
        return None
    
    async def _calculate_indicator(self, symbol: str, indicator: str) -> Optional[Dict]:
        """Calcula un indicador técnico para un activo"""
        try:
            params = {"length": 14} if indicator in ["RSI", "SMA", "EMA"] else {}
            data = apply_indicator(symbol, "1D", indicator, params)
            if data and "latest_value" in data:
                return data
        except Exception as e:
            logger.error(f"Error calculando {indicator} para {symbol}: {e}")
        return None
    
    async def _save_market_data(self, symbol: str, name: str, price_data: Dict):
        """Guarda datos de mercado en la base de datos"""
        try:
            latest_price = price_data.get("latest_price")
            data_points = price_data.get("data", [])
            
            # Calcular cambio si hay datos históricos
            change_24h = 0
            change_percent_24h = 0
            if len(data_points) >= 2:
                current = data_points[-1].get("close", latest_price)
                previous = data_points[-2].get("close", current)

                # Validar que los valores no sean None antes de hacer cálculos
                if current is not None and previous is not None:
                    try:
                        change_24h = float(current) - float(previous)
                        change_percent_24h = (change_24h / float(previous)) * 100 if float(previous) != 0 else 0
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error calculando cambio para {symbol}: {e}")
                        change_24h = 0
                        change_percent_24h = 0
            
            market_data = {
                "symbol": symbol,
                "name": name,
                "current_price": latest_price,
                "change_24h": change_24h,
                "change_percent_24h": change_percent_24h,
                "last_updated": datetime.now().isoformat()
            }

            # Sanitizar datos para JSON
            market_data = sanitize_dict_for_json(market_data)
            
            # Upsert (insert or update)
            result = self.supabase.table("market_data").upsert(
                market_data, 
                on_conflict="symbol"
            ).execute()
            
            logger.debug(f"💾 Guardado precio de {symbol}: ${latest_price}")
            
        except Exception as e:
            logger.error(f"Error guardando datos de {symbol}: {e}")
            raise
    
    async def _save_indicator_data(self, symbol: str, indicator: str, indicator_data: Dict):
        """Guarda datos de indicador técnico en la base de datos"""
        try:
            latest_value = indicator_data.get("latest_value", {})
            value = latest_value.get("value") if latest_value else None
            
            if value is not None:
                # Generar interpretación
                interpretation = self._generate_interpretation(indicator, value)
                
                technical_data = {
                    "symbol": symbol,
                    "indicator_name": indicator,
                    "value": value,
                    "interpretation": interpretation,
                    "calculated_at": datetime.now().isoformat()
                }
                
                # Upsert (insert or update)
                result = self.supabase.table("technical_indicators").upsert(
                    technical_data,
                    on_conflict="symbol,indicator_name"
                ).execute()
                
                logger.debug(f"💾 Guardado {indicator} de {symbol}: {value}")
                
        except Exception as e:
            logger.error(f"Error guardando {indicator} de {symbol}: {e}")
            raise
    
    def _generate_interpretation(self, indicator: str, value: float) -> str:
        """Genera interpretación textual avanzada de un indicador"""
        indicator = indicator.upper()

        if indicator == "RSI":
            if value > 80:
                return "Sobrecompra extrema - Señal de venta fuerte"
            elif value > 70:
                return "Sobrecompra - Posible señal de venta"
            elif value > 50:
                return "Zona alcista - Momentum positivo"
            elif value > 30:
                return "Zona neutral - Sin señales claras"
            elif value > 20:
                return "Sobreventa - Posible señal de compra"
            else:
                return "Sobreventa extrema - Señal de compra fuerte"

        elif indicator == "MACD":
            if value > 2:
                return "Momentum alcista fuerte"
            elif value > 0:
                return "Tendencia alcista"
            elif value > -2:
                return "Tendencia bajista"
            else:
                return "Momentum bajista fuerte"

        elif indicator == "STOCH":
            if value > 80:
                return "Sobrecompra - Considerar venta"
            elif value < 20:
                return "Sobreventa - Considerar compra"
            else:
                return "Zona neutral"

        elif indicator == "ATR":
            if value > 5:
                return "Alta volatilidad - Mayor riesgo"
            elif value > 2:
                return "Volatilidad moderada"
            else:
                return "Baja volatilidad - Mercado estable"

        elif indicator == "ADX":
            if value > 50:
                return "Tendencia muy fuerte"
            elif value > 25:
                return "Tendencia fuerte"
            elif value > 20:
                return "Tendencia moderada"
            else:
                return "Sin tendencia clara - Mercado lateral"

        elif indicator in ["SMA", "EMA"]:
            return f"Media móvil: ${value:.2f}"

        elif indicator == "BBANDS":
            return f"Banda de Bollinger: ${value:.2f}"

        else:
            return f"Valor: {value:.2f}"
    
    async def get_market_context_for_ai(self) -> str:
        """
        Obtiene contexto de mercado para incluir en el system instruction de la IA
        """
        try:
            # Obtener datos recientes (últimas 2 horas)
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()
            
            # Datos de mercado
            market_result = self.supabase.table("market_data").select("*").gte(
                "last_updated", cutoff_time
            ).execute()
            
            # Indicadores técnicos
            indicators_result = self.supabase.table("technical_indicators").select("*").gte(
                "calculated_at", cutoff_time
            ).execute()
            
            # Formatear contexto OPTIMIZADO para muchos activos
            context = "DATOS DE MERCADO ACTUALES:\n\n"

            if market_result.data:
                # Categorizar activos por tipo
                tech_stocks = [item for item in market_result.data if "NASDAQ:" in item["symbol"]][:8]
                crypto = [item for item in market_result.data if "BINANCE:" in item["symbol"]][:6]
                indices = [item for item in market_result.data if item["symbol"] in ["NASDAQ:QQQ", "NYSE:SPY", "NYSE:DIA"]]
                forex = [item for item in market_result.data if "FX:" in item["symbol"]][:3]

                if tech_stocks:
                    context += "TECNOLOGÍA: "
                    for item in tech_stocks:
                        symbol = item["symbol"].replace("NASDAQ:", "")
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        context += f"{symbol}:${price:.0f}({change:+.1f}%) "
                    context += "\n"

                if crypto:
                    context += "CRIPTO: "
                    for item in crypto:
                        symbol = item["symbol"].replace("BINANCE:", "").replace("USDT", "")
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        if price > 1:
                            context += f"{symbol}:${price:.0f}({change:+.1f}%) "
                        else:
                            context += f"{symbol}:${price:.3f}({change:+.1f}%) "
                    context += "\n"

                if indices:
                    context += "ÍNDICES: "
                    for item in indices:
                        symbol = item["symbol"].split(":")[-1]
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        context += f"{symbol}:${price:.0f}({change:+.1f}%) "
                    context += "\n"

                if forex:
                    context += "FOREX: "
                    for item in forex:
                        symbol = item["symbol"].replace("FX:", "")
                        price = item["current_price"]
                        change = item["change_percent_24h"]
                        context += f"{symbol}:{price:.4f}({change:+.1f}%) "
                    context += "\n"

            if indicators_result.data:
                # Organizar indicadores por símbolo para activos principales
                main_symbols = ["NASDAQ:AAPL", "NASDAQ:TSLA", "NASDAQ:MSFT", "NASDAQ:GOOGL",
                               "NASDAQ:AMZN", "NASDAQ:NVDA", "NASDAQ:META", "NASDAQ:NFLX",
                               "BINANCE:BTCUSDT", "BINANCE:ETHUSDT", "BINANCE:BNBUSDT",
                               "BINANCE:ADAUSDT", "BINANCE:SOLUSDT", "BINANCE:XRPUSDT"]

                indicators_by_symbol = {}
                for indicator in indicators_result.data:
                    if indicator["symbol"] in main_symbols:
                        symbol = indicator["symbol"]
                        if symbol not in indicators_by_symbol:
                            indicators_by_symbol[symbol] = {}
                        indicators_by_symbol[symbol][indicator["indicator_name"]] = indicator["value"]

                # Mostrar indicadores técnicos completos
                context += "\nINDICADORES TÉCNICOS:\n"

                for symbol, indicators in indicators_by_symbol.items():
                    clean_symbol = symbol.replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                    context += f"{clean_symbol}: "

                    # RSI (Sobrecompra/Sobreventa)
                    if "RSI" in indicators:
                        rsi = indicators["RSI"]
                        if rsi > 70:
                            context += f"RSI:{rsi:.0f}(sobrecompra) "
                        elif rsi < 30:
                            context += f"RSI:{rsi:.0f}(sobreventa) "
                        else:
                            context += f"RSI:{rsi:.0f} "

                    # MACD (Momentum)
                    if "MACD" in indicators:
                        macd = indicators["MACD"]
                        if macd > 0:
                            context += f"MACD:{macd:.1f}(alcista) "
                        else:
                            context += f"MACD:{macd:.1f}(bajista) "

                    # Estocástico (Momentum)
                    if "STOCH" in indicators:
                        stoch = indicators["STOCH"]
                        if stoch > 80:
                            context += f"STOCH:{stoch:.0f}(sobrecompra) "
                        elif stoch < 20:
                            context += f"STOCH:{stoch:.0f}(sobreventa) "
                        else:
                            context += f"STOCH:{stoch:.0f} "

                    # ATR (Volatilidad)
                    if "ATR" in indicators:
                        atr = indicators["ATR"]
                        if atr > 5:
                            context += f"ATR:{atr:.1f}(alta_vol) "
                        elif atr > 2:
                            context += f"ATR:{atr:.1f}(mod_vol) "
                        else:
                            context += f"ATR:{atr:.1f}(baja_vol) "

                    # ADX (Fuerza de tendencia)
                    if "ADX" in indicators:
                        adx = indicators["ADX"]
                        if adx > 25:
                            context += f"ADX:{adx:.0f}(tendencia_fuerte) "
                        else:
                            context += f"ADX:{adx:.0f}(sin_tendencia) "

                    # Medias móviles (solo mostrar una para no saturar)
                    if "SMA" in indicators:
                        sma = indicators["SMA"]
                        context += f"SMA:{sma:.0f} "

                    context += "\n"

            # Añadir análisis avanzados
            context += await self._get_advanced_analysis_context()

            context += f"Actualizado: {datetime.now().strftime('%H:%M')}\n"
            context += f"Total activos: {len(market_result.data) if market_result.data else 0}\n"

            return context
            
        except Exception as e:
            logger.error(f"Error obteniendo contexto de mercado: {e}")
            return "Datos de mercado no disponibles en este momento.\n"

    async def _get_advanced_analysis_context(self) -> str:
        """
        Obtiene contexto de análisis avanzados para la IA.
        """
        try:
            context = ""
            cutoff_time = (datetime.now() - timedelta(hours=6)).isoformat()

            # 1. Análisis de volumen
            volume_result = self.supabase.table("volume_analysis").select("*").gte(
                "analyzed_at", cutoff_time
            ).limit(10).execute()

            if volume_result.data:
                context += "\nANÁLISIS DE VOLUMEN:\n"
                for item in volume_result.data:
                    symbol = item["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                    vwap = item.get("vwap_value")
                    confirmation = "✓" if item.get("volume_confirmation") else "✗"
                    if vwap:
                        context += f"{symbol}: VWAP:${vwap:.0f} Vol:{confirmation} "
                context += "\n"

            # 2. Patrones detectados
            patterns_result = self.supabase.table("pattern_analysis").select("*").gte(
                "detected_at", cutoff_time
            ).order("confidence_score", desc=True).limit(8).execute()

            if patterns_result.data:
                context += "\nPATRONES DETECTADOS:\n"
                for item in patterns_result.data:
                    symbol = item["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                    pattern = item["pattern_type"].replace("_", " ")
                    confidence = item.get("confidence_score", 0)
                    context += f"{symbol}: {pattern}({confidence:.0f}%) "
                context += "\n"

            # 3. Análisis multi-timeframe
            mtf_result = self.supabase.table("multi_timeframe_analysis").select("*").gte(
                "analyzed_at", cutoff_time
            ).limit(8).execute()

            if mtf_result.data:
                context += "\nCONFLUENCIAS MULTI-TIMEFRAME:\n"
                for item in mtf_result.data:
                    symbol = item["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                    direction = item["confluence_direction"]
                    percentage = item.get("confluence_percentage", 0)
                    strength = item["confluence_strength"]
                    context += f"{symbol}: {direction}({percentage:.0f}%,{strength}) "
                context += "\n"

            # 4. Estructura de mercado
            ms_result = self.supabase.table("market_structure_analysis").select("*").gte(
                "analyzed_at", cutoff_time
            ).limit(8).execute()

            if ms_result.data:
                context += "\nESTRUCTURA DE MERCADO:\n"
                for item in ms_result.data:
                    symbol = item["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                    trend = item["trend_structure"]
                    phase = item["market_phase"]
                    bias = item["overall_bias"]
                    context += f"{symbol}: {trend},{phase},{bias} "
                context += "\n"

            return context

        except Exception as e:
            logger.error(f"Error obteniendo contexto de análisis avanzados: {e}")
            return "\nANÁLISIS AVANZADOS: No disponibles temporalmente.\n"

    async def generate_market_analysis(self) -> str:
        """
        Genera análisis general de mercado con tendencias y alertas
        """
        try:
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()

            # Obtener datos recientes
            market_result = self.supabase.table("market_data").select("*").gte(
                "last_updated", cutoff_time
            ).execute()

            indicators_result = self.supabase.table("technical_indicators").select("*").gte(
                "calculated_at", cutoff_time
            ).execute()

            if not market_result.data:
                return "No hay datos de mercado disponibles para el análisis."

            # Análisis de tendencia general
            positive_changes = len([item for item in market_result.data if item["change_percent_24h"] > 0])
            total_assets = len(market_result.data)
            market_sentiment = "Alcista" if positive_changes > total_assets * 0.6 else "Bajista" if positive_changes < total_assets * 0.4 else "Neutral"

            # Mejores y peores performers
            sorted_by_change = sorted(market_result.data, key=lambda x: x["change_percent_24h"], reverse=True)
            top_gainers = sorted_by_change[:3]
            top_losers = sorted_by_change[-3:]

            # Análisis de indicadores
            rsi_data = [item for item in indicators_result.data if item["indicator_name"] == "RSI"]
            overbought = len([item for item in rsi_data if item["value"] > 70])
            oversold = len([item for item in rsi_data if item["value"] < 30])

            macd_data = [item for item in indicators_result.data if item["indicator_name"] == "MACD"]
            strong_momentum = len([item for item in macd_data if item["value"] > 2])

            # Generar análisis
            analysis = f"""📊 ANÁLISIS GENERAL DE MERCADO - {datetime.now().strftime('%d %B %Y, %H:%M')}

🎯 TENDENCIA GENERAL: {market_sentiment}
• {positive_changes}/{total_assets} activos en positivo ({positive_changes/total_assets*100:.0f}%)

🚀 MEJORES PERFORMERS:"""

            for gainer in top_gainers:
                symbol = gainer["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                analysis += f"\n• {symbol}: {gainer['change_percent_24h']:+.1f}%"

            analysis += f"\n\n📉 MAYORES CAÍDAS:"
            for loser in top_losers:
                symbol = loser["symbol"].replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                analysis += f"\n• {symbol}: {loser['change_percent_24h']:+.1f}%"

            analysis += f"""

⚠️ ALERTAS TÉCNICAS:
• {overbought} activos en sobrecompra (RSI >70)
• {oversold} activos en sobreventa (RSI <30)
• {strong_momentum} activos con momentum alcista fuerte (MACD >2)

📈 Total activos monitoreados: {total_assets}
🕐 Última actualización: {datetime.now().strftime('%H:%M')}

Esta información es solo para fines educativos y no constituye asesoramiento financiero."""

            return analysis

        except Exception as e:
            logger.error(f"Error generando análisis de mercado: {e}")
            return "Error al generar el análisis de mercado."

    async def generate_buy_opportunities(self) -> str:
        """
        Analiza todos los activos y genera recomendaciones de compra
        basadas en múltiples indicadores técnicos
        """
        try:
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()

            # Obtener datos recientes
            market_result = self.supabase.table("market_data").select("*").gte(
                "last_updated", cutoff_time
            ).execute()

            indicators_result = self.supabase.table("technical_indicators").select("*").gte(
                "calculated_at", cutoff_time
            ).execute()

            if not market_result.data or not indicators_result.data:
                return "No hay suficientes datos para generar recomendaciones."

            # Organizar indicadores por símbolo
            indicators_by_symbol = {}
            for indicator in indicators_result.data:
                symbol = indicator["symbol"]
                if symbol not in indicators_by_symbol:
                    indicators_by_symbol[symbol] = {}
                indicators_by_symbol[symbol][indicator["indicator_name"]] = indicator["value"]

            # Analizar cada activo
            high_priority = []
            medium_priority = []
            avoid_list = []

            for asset in market_result.data:
                symbol = asset["symbol"]
                name = asset["name"]
                price = asset["current_price"]
                change = asset["change_percent_24h"]

                # Obtener indicadores
                indicators = indicators_by_symbol.get(symbol, {})
                rsi = indicators.get("RSI")
                macd = indicators.get("MACD")
                stoch = indicators.get("STOCH")

                if not rsi:  # Skip si no hay indicadores
                    continue

                # Lógica de scoring
                score = 0
                reasons = []

                # RSI scoring
                if rsi < 30:
                    score += 3
                    reasons.append("RSI sobreventa")
                elif 30 <= rsi <= 50:
                    score += 2
                    reasons.append("RSI neutral-alcista")
                elif 50 < rsi <= 65:
                    score += 1
                    reasons.append("RSI zona alcista")
                elif rsi > 70:
                    score -= 2
                    reasons.append("RSI sobrecompra")

                # MACD scoring
                if macd and macd > 0:
                    score += 1
                    reasons.append("MACD positivo")
                elif macd and macd < -1:
                    score -= 1

                # Momentum reciente
                if change > 0:
                    score += 0.5
                elif change < -3:
                    score -= 1

                # Clasificar
                clean_symbol = symbol.replace("NASDAQ:", "").replace("BINANCE:", "").replace("USDT", "")
                asset_info = {
                    "symbol": clean_symbol,
                    "name": name,
                    "price": price,
                    "change": change,
                    "rsi": rsi,
                    "macd": macd,
                    "score": score,
                    "reasons": reasons
                }

                if score >= 3:
                    high_priority.append(asset_info)
                elif score >= 1:
                    medium_priority.append(asset_info)
                elif score < 0:
                    avoid_list.append(asset_info)

            # Ordenar por score
            high_priority.sort(key=lambda x: x["score"], reverse=True)
            medium_priority.sort(key=lambda x: x["score"], reverse=True)
            avoid_list.sort(key=lambda x: x["score"])

            # Generar recomendaciones
            recommendations = f"""🎯 OPORTUNIDADES DE COMPRA DETECTADAS - {datetime.now().strftime('%H:%M')}

🥇 ALTA PRIORIDAD ({len(high_priority)} activos):"""

            for asset in high_priority[:5]:  # Top 5
                recommendations += f"""
• {asset['symbol']} (${asset['price']:.2f}): {asset['change']:+.1f}%
  RSI: {asset['rsi']:.0f} | MACD: {asset['macd']:.2f if asset['macd'] else 'N/A'}
  Razones: {', '.join(asset['reasons'])}"""

            recommendations += f"\n\n🥈 MEDIA PRIORIDAD ({len(medium_priority)} activos):"

            for asset in medium_priority[:5]:  # Top 5
                recommendations += f"""
• {asset['symbol']} (${asset['price']:.2f}): {asset['change']:+.1f}%
  RSI: {asset['rsi']:.0f} | Razones: {', '.join(asset['reasons'])}"""

            if avoid_list:
                recommendations += f"\n\n⚠️ EVITAR POR AHORA ({len(avoid_list)} activos):"
                for asset in avoid_list[:3]:  # Top 3 a evitar
                    recommendations += f"""
• {asset['symbol']}: RSI {asset['rsi']:.0f} ({', '.join(asset['reasons'])})"""

            recommendations += f"""

📋 CRITERIOS DE ANÁLISIS:
• RSI: Sobreventa (<30) = Oportunidad | Sobrecompra (>70) = Evitar
• MACD: Positivo = Momentum alcista
• Cambio reciente: Considerado en scoring

⚠️ IMPORTANTE: Esta información es solo para fines educativos.
Siempre realiza tu propio análisis antes de invertir."""

            return recommendations

        except Exception as e:
            logger.error(f"Error generando oportunidades de compra: {e}")
            return "Error al generar recomendaciones de compra."

    async def update_advanced_analysis(self, batch_size: int = 5) -> Dict[str, Any]:
        """
        Actualiza análisis avanzados para activos principales y los almacena en Supabase.
        Incluye análisis de volumen, patrones, multi-timeframe y estructura de mercado.
        """
        results = {
            "updated_volume_analysis": 0,
            "updated_patterns": 0,
            "updated_multi_timeframe": 0,
            "updated_support_resistance": 0,
            "updated_market_structure": 0,
            "errors": [],
            "total_assets": len(POPULAR_ASSETS)  # Analizar TODOS los activos populares
        }

        logger.info(f"🔄 Iniciando análisis avanzado para {results['total_assets']} activos...")

        # Procesar TODOS los activos populares para análisis avanzado
        main_assets = POPULAR_ASSETS

        for i in range(0, len(main_assets), batch_size):
            batch = main_assets[i:i + batch_size]
            logger.info(f"📦 Procesando lote {i//batch_size + 1}/{(len(main_assets) + batch_size - 1)//batch_size}")

            for asset in batch:
                symbol = asset["symbol"]
                name = asset["name"]

                try:
                    # 1. Análisis de volumen
                    await self._update_volume_analysis(symbol)
                    results["updated_volume_analysis"] += 1

                    # 2. Detección de patrones (para TODOS los activos)
                    await self._update_pattern_analysis(symbol)
                    results["updated_patterns"] += 1

                    # 3. Análisis multi-timeframe
                    await self._update_multi_timeframe_analysis(symbol)
                    results["updated_multi_timeframe"] += 1

                    # 4. Soporte y resistencia
                    await self._update_support_resistance(symbol)
                    results["updated_support_resistance"] += 1

                    # 5. Estructura de mercado
                    await self._update_market_structure(symbol)
                    results["updated_market_structure"] += 1

                    logger.info(f"✅ Análisis avanzado completado para {symbol}")

                except Exception as e:
                    error_msg = f"Error en análisis avanzado de {symbol}: {str(e)}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)

                # Pequeña pausa para no sobrecargar las APIs
                await asyncio.sleep(0.5)

            # Pausa entre lotes
            await asyncio.sleep(2)

        logger.info(f"🎯 Análisis avanzado completado: {results}")
        return results

    async def clean_all_tables(self) -> Dict[str, Any]:
        """
        Limpia completamente todas las tablas de datos de mercado.

        Esta función elimina todos los datos existentes para garantizar
        una recarga completamente fresca y consistente.

        Returns:
            Dict con estadísticas de limpieza
        """
        logger.info("🧹 INICIANDO LIMPIEZA COMPLETA DE TABLAS")

        tables_to_clean = [
            "market_data",
            "technical_indicators",
            "volume_analysis",
            "multi_timeframe_analysis",
            "pattern_analysis",
            "market_structure_analysis",
            "support_resistance_levels"
        ]

        cleaning_results = {
            "cleaned_tables": [],
            "cleaning_errors": [],
            "total_tables_cleaned": 0,
            "total_records_deleted": 0
        }

        for table_name in tables_to_clean:
            try:
                logger.info(f"🗑️ Limpiando tabla: {table_name}")

                # Contar registros antes de eliminar
                try:
                    count_result = self.supabase.table(table_name).select("id", count="exact").execute()
                    records_before = count_result.count if hasattr(count_result, 'count') else 0
                except:
                    records_before = 0

                # Eliminar todos los registros
                self.supabase.table(table_name).delete().neq("id", "00000000-0000-0000-0000-000000000000").execute()

                cleaning_results["cleaned_tables"].append({
                    "table": table_name,
                    "records_deleted": records_before,
                    "status": "success"
                })
                cleaning_results["total_records_deleted"] += records_before
                cleaning_results["total_tables_cleaned"] += 1

                logger.info(f"✅ {table_name}: {records_before} registros eliminados")

            except Exception as e:
                error_msg = f"Error limpiando {table_name}: {str(e)}"
                logger.error(error_msg)
                cleaning_results["cleaning_errors"].append(error_msg)
                cleaning_results["cleaned_tables"].append({
                    "table": table_name,
                    "records_deleted": 0,
                    "status": "error",
                    "error": str(e)
                })

        logger.info(f"🧹 LIMPIEZA COMPLETADA: {cleaning_results['total_tables_cleaned']} tablas, {cleaning_results['total_records_deleted']} registros eliminados")
        return cleaning_results

    async def full_data_reload(self) -> Dict[str, Any]:
        """
        Ejecuta una recarga completa de todos los datos de mercado.

        Esta función realiza:
        0. Limpieza completa de todas las tablas
        1. Actualización completa de datos básicos e indicadores técnicos
        2. Análisis avanzados para todos los activos
        3. Cálculos de volumen, patrones, multi-timeframe, etc.

        Returns:
            Dict con resultados detallados de la recarga completa
        """
        start_time = datetime.now()
        logger.info("🚀 INICIANDO RECARGA COMPLETA DE DATOS")

        results = {
            "cleaning_results": {},
            "basic_data": {},
            "advanced_analysis": {},
            "total_time_seconds": 0,
            "start_time": start_time.isoformat(),
            "end_time": None,
            "status": "success",
            "summary": {}
        }

        try:
            # 0. LIMPIEZA COMPLETA DE TABLAS
            logger.info("🧹 Fase 0: Limpieza completa de tablas...")
            cleaning_results = await self.clean_all_tables()
            results["cleaning_results"] = cleaning_results

            # 1. Actualización de datos básicos y indicadores técnicos
            logger.info("📊 Fase 1: Actualizando datos básicos e indicadores...")
            basic_results = await self.update_market_data()
            results["basic_data"] = basic_results

            # 2. Análisis avanzados completos
            logger.info("🧠 Fase 2: Ejecutando análisis avanzados...")
            advanced_results = await self.update_advanced_analysis(batch_size=10)
            results["advanced_analysis"] = advanced_results

            # 3. Calcular estadísticas finales
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()

            results.update({
                "total_time_seconds": round(total_time, 2),
                "end_time": end_time.isoformat(),
                "summary": {
                    "total_assets_processed": basic_results.get("updated_assets", 0),
                    "total_indicators_calculated": basic_results.get("updated_indicators", 0),
                    "total_volume_analysis": advanced_results.get("updated_volume_analysis", 0),
                    "total_patterns_detected": advanced_results.get("updated_patterns", 0),
                    "total_multi_timeframe": advanced_results.get("updated_multi_timeframe", 0),
                    "total_support_resistance": advanced_results.get("updated_support_resistance", 0),
                    "total_market_structure": advanced_results.get("updated_market_structure", 0),
                    "total_errors": len(basic_results.get("errors", [])) + len(advanced_results.get("errors", [])),
                    "success_rate": self._calculate_success_rate(basic_results, advanced_results),
                    "tables_cleaned": cleaning_results.get("total_tables_cleaned", 0),
                    "records_deleted": cleaning_results.get("total_records_deleted", 0)
                }
            })

            logger.info(f"✅ RECARGA COMPLETA FINALIZADA en {total_time:.2f} segundos")
            logger.info(f"🧹 Limpieza: {results['summary']['tables_cleaned']} tablas, {results['summary']['records_deleted']} registros eliminados")
            logger.info(f"📈 Resumen: {results['summary']['total_assets_processed']} activos, "
                       f"{results['summary']['total_indicators_calculated']} indicadores, "
                       f"{results['summary']['success_rate']:.1f}% éxito")

            return results

        except Exception as e:
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()

            logger.error(f"❌ Error en recarga completa: {str(e)}")

            results.update({
                "status": "error",
                "error": str(e),
                "total_time_seconds": round(total_time, 2),
                "end_time": end_time.isoformat()
            })

            return results

    def _calculate_success_rate(self, basic_results: Dict, advanced_results: Dict) -> float:
        """Calcula la tasa de éxito general de la recarga"""
        total_operations = (
            basic_results.get("updated_assets", 0) +
            basic_results.get("updated_indicators", 0) +
            advanced_results.get("updated_volume_analysis", 0) +
            advanced_results.get("updated_patterns", 0) +
            advanced_results.get("updated_multi_timeframe", 0) +
            advanced_results.get("updated_support_resistance", 0) +
            advanced_results.get("updated_market_structure", 0)
        )

        total_errors = len(basic_results.get("errors", [])) + len(advanced_results.get("errors", []))

        if total_operations == 0:
            return 0.0

        success_operations = total_operations - total_errors
        return (success_operations / total_operations) * 100

    async def _update_volume_analysis(self, symbol: str):
        """Actualiza análisis de volumen para un símbolo."""
        try:
            # Calcular VWAP
            vwap_data = calculate_vwap(symbol, "1D", 50)

            # Calcular confirmación de volumen
            volume_conf = analyze_volume_confirmation(symbol, "1D", 30)

            # Guardar en base de datos
            volume_analysis = {
                "symbol": symbol,
                "timeframe": "1D",
                "volume_trend": volume_conf.get("volume_confirmation", "UNKNOWN"),
                "volume_confirmation": volume_conf.get("volume_confirmation") == "CONFIRMED",
                "vwap_value": vwap_data.get("current_vwap"),
                "analyzed_at": datetime.now().isoformat()
            }

            # Insertar en Supabase (reemplazar si existe)
            try:
                # Primero intentar eliminar registro existente
                self.supabase.table("volume_analysis").delete().eq(
                    "symbol", symbol
                ).eq("timeframe", "1D").execute()

                # Luego insertar el nuevo
                self.supabase.table("volume_analysis").insert(volume_analysis).execute()
            except Exception as e:
                # Si falla, solo insertar
                self.supabase.table("volume_analysis").insert(volume_analysis).execute()

        except Exception as e:
            logger.error(f"Error actualizando análisis de volumen para {symbol}: {e}")

    async def _update_pattern_analysis(self, symbol: str):
        """Actualiza análisis de patrones para un símbolo."""
        try:
            patterns_data = detect_chart_patterns(symbol, "1D", 100)

            # Guardar cada patrón detectado
            for pattern in patterns_data.get("patterns", []):
                pattern_analysis = {
                    "symbol": symbol,
                    "timeframe": "1D",
                    "pattern_type": pattern.get("pattern_type"),
                    "confidence_score": pattern.get("confidence"),
                    "target_price": pattern.get("target_price"),
                    "detected_at": datetime.now().isoformat()
                }

                self.supabase.table("pattern_analysis").insert(pattern_analysis).execute()

        except Exception as e:
            logger.error(f"Error actualizando análisis de patrones para {symbol}: {e}")

    async def _update_multi_timeframe_analysis(self, symbol: str):
        """Actualiza análisis multi-timeframe para un símbolo."""
        try:
            mtf_data = get_multi_timeframe_analysis(symbol, ["1W", "1D", "4h"], 50)

            overall_analysis = mtf_data.get("overall_analysis", {})

            mtf_analysis = {
                "symbol": symbol,
                "timeframes": ["1W", "1D", "4h"],
                "confluence_direction": overall_analysis.get("confluence_direction", "NEUTRAL"),
                "confluence_percentage": overall_analysis.get("confluence_percentage", 0),
                "confluence_strength": overall_analysis.get("confluence_strength", "WEAK"),
                "overall_recommendation": mtf_data.get("recommendation", {}).get("action", "HOLD"),
                "analyzed_at": datetime.now().isoformat()
            }

            # Insertar en Supabase (reemplazar si existe)
            try:
                # Primero intentar eliminar registro existente
                self.supabase.table("multi_timeframe_analysis").delete().eq(
                    "symbol", symbol
                ).execute()

                # Luego insertar el nuevo
                self.supabase.table("multi_timeframe_analysis").insert(mtf_analysis).execute()
            except Exception as e:
                # Si falla, solo insertar
                self.supabase.table("multi_timeframe_analysis").insert(mtf_analysis).execute()

        except Exception as e:
            logger.error(f"Error actualizando análisis multi-timeframe para {symbol}: {e}")

    async def _update_support_resistance(self, symbol: str):
        """Actualiza niveles de soporte y resistencia para un símbolo."""
        try:
            sr_data = identify_support_resistance(symbol, "1D", 200)

            # Limpiar niveles antiguos
            self.supabase.table("support_resistance_levels").delete().eq("symbol", symbol).execute()

            # Guardar niveles de resistencia
            for level in sr_data.get("resistance_levels", []):
                resistance_level = {
                    "symbol": symbol,
                    "timeframe": "1D",
                    "level_type": "RESISTANCE",
                    "price_level": level.get("price"),
                    "strength": level.get("strength"),
                    "touches": level.get("touches"),
                    "identified_at": datetime.now().isoformat()
                }

                self.supabase.table("support_resistance_levels").insert(resistance_level).execute()

            # Guardar niveles de soporte
            for level in sr_data.get("support_levels", []):
                support_level = {
                    "symbol": symbol,
                    "timeframe": "1D",
                    "level_type": "SUPPORT",
                    "price_level": level.get("price"),
                    "strength": level.get("strength"),
                    "touches": level.get("touches"),
                    "identified_at": datetime.now().isoformat()
                }

                self.supabase.table("support_resistance_levels").insert(support_level).execute()

        except Exception as e:
            logger.error(f"Error actualizando soporte/resistencia para {symbol}: {e}")

    async def _update_market_structure(self, symbol: str):
        """Actualiza análisis de estructura de mercado para un símbolo."""
        try:
            ms_data = analyze_market_structure(symbol, "1D", 100)

            trend_structure = ms_data.get("trend_structure", {})
            market_phase = ms_data.get("market_phase", {})
            interpretation = ms_data.get("interpretation", {})

            market_structure = {
                "symbol": symbol,
                "timeframe": "1D",
                "trend_structure": trend_structure.get("trend", "UNKNOWN"),
                "market_phase": market_phase.get("phase", "UNKNOWN"),
                "phase_confidence": market_phase.get("confidence", 0),
                "overall_bias": interpretation.get("overall_bias", "NEUTRAL"),
                "trend_strength": trend_structure.get("strength", "UNKNOWN"),
                "analyzed_at": datetime.now().isoformat()
            }

            # Insertar en Supabase (reemplazar si existe)
            try:
                # Primero intentar eliminar registro existente
                self.supabase.table("market_structure_analysis").delete().eq(
                    "symbol", symbol
                ).eq("timeframe", "1D").execute()

                # Luego insertar el nuevo
                self.supabase.table("market_structure_analysis").insert(market_structure).execute()
            except Exception as e:
                # Si falla, solo insertar
                self.supabase.table("market_structure_analysis").insert(market_structure).execute()

        except Exception as e:
            logger.error(f"Error actualizando estructura de mercado para {symbol}: {e}")

# Instancia global del servicio
market_data_service = MarketDataService()
