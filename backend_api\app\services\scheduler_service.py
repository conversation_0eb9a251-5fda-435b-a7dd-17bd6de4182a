"""
Servicio centralizado para manejo del scheduler de datos de mercado
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from enum import Enum

from app.services.market_data_service import market_data_service

logger = logging.getLogger(__name__)

class SchedulerStatus(Enum):
    STOPPED = "stopped"
    RUNNING = "running"
    ERROR = "error"
    PAUSED = "paused"

class SchedulerService:
    """Servicio centralizado para gestión del scheduler"""
    
    def __init__(self):
        self.status = SchedulerStatus.STOPPED
        self.thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.last_run: Optional[datetime] = None
        self.next_run: Optional[datetime] = None
        self.total_runs = 0
        self.error_count = 0
        self.config = {
            "interval_minutes": 15,
            "market_hours_only": True,
            "enabled": True,
            "advanced_analysis_interval": 60,  # An<PERSON><PERSON><PERSON> avan<PERSON> cada 60 minutos
            "enable_advanced_analysis": True
        }
        self.last_advanced_analysis: Optional[datetime] = None
    
    def start(self) -> Dict[str, Any]:
        """Inicia el scheduler"""
        try:
            if self.status == SchedulerStatus.RUNNING:
                return {"status": "already_running", "message": "Scheduler ya está ejecutándose"}
            
            self.stop_event.clear()
            self.status = SchedulerStatus.RUNNING
            
            # Crear y iniciar thread
            self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.thread.start()
            
            logger.info("🚀 Scheduler iniciado correctamente")
            return {"status": "started", "message": "Scheduler iniciado correctamente"}
            
        except Exception as e:
            self.status = SchedulerStatus.ERROR
            logger.error(f"❌ Error iniciando scheduler: {e}")
            return {"status": "error", "message": f"Error iniciando scheduler: {str(e)}"}
    
    def stop(self) -> Dict[str, Any]:
        """Detiene el scheduler"""
        try:
            if self.status == SchedulerStatus.STOPPED:
                return {"status": "already_stopped", "message": "Scheduler ya está detenido"}
            
            self.stop_event.set()
            self.status = SchedulerStatus.STOPPED
            
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=10)
            
            logger.info("🛑 Scheduler detenido correctamente")
            return {"status": "stopped", "message": "Scheduler detenido correctamente"}
            
        except Exception as e:
            self.status = SchedulerStatus.ERROR
            logger.error(f"❌ Error deteniendo scheduler: {e}")
            return {"status": "error", "message": f"Error deteniendo scheduler: {str(e)}"}
    
    def get_status(self) -> Dict[str, Any]:
        """Obtiene el estado actual del scheduler"""
        return {
            "status": self.status.value,
            "last_run": self.last_run.isoformat() if self.last_run else None,
            "next_run": self.next_run.isoformat() if self.next_run else None,
            "total_runs": self.total_runs,
            "error_count": self.error_count,
            "config": self.config,
            "thread_alive": self.thread.is_alive() if self.thread else False
        }
    
    def update_config(self, new_config: Dict[str, Any]) -> Dict[str, Any]:
        """Actualiza la configuración del scheduler"""
        try:
            self.config.update(new_config)
            logger.info(f"⚙️ Configuración actualizada: {new_config}")
            return {"status": "updated", "config": self.config}
        except Exception as e:
            logger.error(f"❌ Error actualizando configuración: {e}")
            return {"status": "error", "message": str(e)}
    
    def _run_scheduler(self):
        """Loop principal del scheduler (ejecuta en thread separado)"""
        logger.info("⏰ Iniciando loop del scheduler...")
        
        while not self.stop_event.is_set():
            try:
                if self._should_run_update():
                    self._calculate_next_run()
                    asyncio.run(self._execute_update())
                
                # Esperar 1 minuto antes de verificar de nuevo
                if self.stop_event.wait(60):
                    break
                    
            except Exception as e:
                self.error_count += 1
                logger.error(f"❌ Error en loop del scheduler: {e}")
                self.status = SchedulerStatus.ERROR
                
                # Esperar 5 minutos antes de reintentar
                if self.stop_event.wait(300):
                    break
                
                self.status = SchedulerStatus.RUNNING
    
    def _should_run_update(self) -> bool:
        """Determina si debe ejecutar una actualización"""
        now = datetime.now()
        
        # Si no hay próxima ejecución programada, calcularla
        if not self.next_run:
            self._calculate_next_run()
            return False
        
        # Verificar si es hora de ejecutar
        return now >= self.next_run
    
    def _calculate_next_run(self):
        """Calcula la próxima ejecución"""
        now = datetime.now()
        interval_minutes = self.config.get("interval_minutes", 15)
        
        if self.config.get("market_hours_only", True):
            # Solo durante horas de mercado (9:00-16:00, Lunes-Viernes)
            if now.weekday() >= 5:  # Fin de semana
                # Próximo lunes a las 9:00
                days_until_monday = 7 - now.weekday()
                next_monday = now + timedelta(days=days_until_monday)
                self.next_run = next_monday.replace(hour=9, minute=0, second=0, microsecond=0)
            elif now.hour < 9:
                # Antes de apertura, programar para las 9:00
                self.next_run = now.replace(hour=9, minute=0, second=0, microsecond=0)
            elif now.hour >= 16:
                # Después de cierre, programar para mañana a las 9:00
                tomorrow = now + timedelta(days=1)
                if tomorrow.weekday() >= 5:  # Si mañana es fin de semana
                    days_until_monday = 7 - tomorrow.weekday()
                    next_monday = tomorrow + timedelta(days=days_until_monday)
                    self.next_run = next_monday.replace(hour=9, minute=0, second=0, microsecond=0)
                else:
                    self.next_run = tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)
            else:
                # Durante horas de mercado, próxima ejecución en X minutos
                self.next_run = now + timedelta(minutes=interval_minutes)
        else:
            # Ejecutar cada X minutos sin restricciones
            self.next_run = now + timedelta(minutes=interval_minutes)
    
    async def _execute_update(self):
        """Ejecuta la actualización de datos"""
        try:
            logger.info("🔄 Ejecutando actualización programada...")
            self.last_run = datetime.now()

            # 1. Actualización regular de datos de mercado
            result = await market_data_service.update_market_data()

            # 2. Verificar si es hora de ejecutar análisis avanzados
            if self._should_run_advanced_analysis():
                logger.info("🧠 Ejecutando análisis avanzados...")
                try:
                    advanced_result = await market_data_service.update_advanced_analysis(batch_size=3)
                    self.last_advanced_analysis = datetime.now()
                    logger.info(f"✅ Análisis avanzados completados: {advanced_result}")
                    result["advanced_analysis"] = advanced_result
                except Exception as e:
                    logger.error(f"❌ Error en análisis avanzados: {e}")
                    result["advanced_analysis_error"] = str(e)

            self.total_runs += 1
            logger.info(f"✅ Actualización completada: {result}")

        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ Error en actualización programada: {e}")
            raise

    def _should_run_advanced_analysis(self) -> bool:
        """Determina si debe ejecutar análisis avanzados"""
        if not self.config.get("enable_advanced_analysis", True):
            return False

        if not self.last_advanced_analysis:
            return True

        now = datetime.now()
        interval_minutes = self.config.get("advanced_analysis_interval", 60)
        next_advanced_run = self.last_advanced_analysis + timedelta(minutes=interval_minutes)

        return now >= next_advanced_run

# Instancia global del servicio
scheduler_service = SchedulerService()
