"""
Supabase client service for TradingIA Backend.

This module provides a centralized interface for all Supabase operations
including authentication, user management, and data persistence.
"""

from supabase import create_client, Client
from fastapi import HTTPException, status
from app.config import settings
from typing import Dict, Any, Optional
import logging
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)


def create_supabase_client() -> Client:
    """
    Initialize and return a Supabase client instance.
    
    Uses the configuration settings to create a client with the
    appropriate credentials for the current environment.
    
    Returns:
        Client: Configured Supabase client instance
        
    Raises:
        HTTPException: If client initialization fails
    """
    try:
        # Create client with URL and service key for server-side operations
        supabase: Client = create_client(
            supabase_url=settings.supabase_url,
            supabase_key=settings.supabase_service_key
        )
        
        logger.info("Supabase client initialized successfully")
        return supabase
        
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database connection failed"
        )


# Global client instance
_supabase_client: Optional[Client] = None


def get_supabase_client() -> Client:
    """
    Get or create a global Supabase client instance.
    
    This function implements a singleton pattern to reuse the same
    client instance across the application.
    
    Returns:
        Client: Supabase client instance
    """
    global _supabase_client
    
    if _supabase_client is None:
        _supabase_client = create_supabase_client()
    
    return _supabase_client


async def validate_user_token(token: str) -> Dict[str, Any]:
    """
    Validate a JWT token and extract user information.

    This function verifies the provided JWT token with Supabase
    and returns the user information if the token is valid.

    Args:
        token (str): JWT token from the Authorization header

    Returns:
        Dict[str, Any]: User information including id, email, and metadata

    Raises:
        HTTPException: If token is invalid or user not found
    """
    try:
        # Remove 'Bearer ' prefix if present
        if token.startswith('Bearer '):
            token = token[7:]

        # Get Supabase client
        supabase = get_supabase_client()

        # Verify the token and get user info
        response = supabase.auth.get_user(token)

        if not response.user:
            logger.warning("Token validation failed: No user found")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token",
                headers={"WWW-Authenticate": "Bearer"}
            )

        user = response.user

        # Extract user information
        user_info = {
            "id": user.id,
            "email": user.email,
            "email_confirmed_at": user.email_confirmed_at,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "user_metadata": user.user_metadata or {},
            "app_metadata": user.app_metadata or {}
        }

        logger.info(f"Token validated successfully for user: {user.email}")
        return user_info

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Token validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def save_chat_history(
    user_id: str,
    request_messages: list,
    ai_response: str,
    conversation_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Save a chat interaction to the database.

    This function stores the user's request and the AI's response
    in the chat_history table, linked to the user's ID.

    Args:
        user_id (str): The ID of the user
        request_messages (list): List of messages in the request
        ai_response (str): The AI-generated response
        conversation_id (str, optional): Conversation session ID

    Returns:
        Dict[str, Any]: Information about the saved record

    Raises:
        HTTPException: If saving fails
    """
    try:
        supabase = get_supabase_client()

        # Prepare the data to save
        chat_data = {
            "user_id": user_id,
            "request_messages": request_messages,
            "ai_response": ai_response,
            "conversation_id": conversation_id,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # Insert into chat_history table
        response = supabase.table("chat_history").insert(chat_data).execute()

        if not response.data:
            logger.error("Failed to save chat history: No data returned")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save chat history"
            )

        saved_record = response.data[0]
        logger.info(f"Chat history saved successfully for user: {user_id}")

        return {
            "id": saved_record.get("id"),
            "created_at": saved_record.get("created_at"),
            "conversation_id": conversation_id
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error saving chat history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save chat history"
        )


async def get_user_chat_history(
    user_id: str,
    limit: int = 50,
    conversation_id: Optional[str] = None
) -> list:
    """
    Retrieve chat history for a user.

    Args:
        user_id (str): The ID of the user
        limit (int): Maximum number of records to return
        conversation_id (str, optional): Filter by conversation ID

    Returns:
        list: List of chat history records

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        supabase = get_supabase_client()

        # Build query
        query = supabase.table("chat_history").select("*").eq("user_id", user_id)

        if conversation_id:
            query = query.eq("conversation_id", conversation_id)

        # Execute query with ordering and limit
        response = query.order("created_at", desc=True).limit(limit).execute()

        logger.info(f"Retrieved {len(response.data)} chat history records for user: {user_id}")
        return response.data

    except Exception as e:
        logger.error(f"Error retrieving chat history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve chat history"
        )
