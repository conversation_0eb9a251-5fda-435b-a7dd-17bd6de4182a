"""
Advanced Technical Indicators for TradingIA Backend.

This module provides advanced and custom technical indicators that are not
available in standard libraries, including composite indicators and signals.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from fastapi import HTTPException, status
import logging
from .tradingview_provider import get_price_data

logger = logging.getLogger(__name__)

def calculate_composite_momentum(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Calculate a composite momentum indicator combining multiple momentum oscillators.
    
    This indicator combines RSI, MACD, and Stochastic to provide a comprehensive
    momentum reading with reduced false signals.
    
    Args:
        symbol (str): Trading symbol
        interval (str): Time interval
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: Composite momentum analysis
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame with validation
        df_data = []
        for bar in price_data["data"]:
            # Validate and sanitize data
            high = bar.get("high")
            low = bar.get("low")
            close = bar.get("close")
            volume = bar.get("volume")

            # Skip bars with None values
            if any(val is None for val in [high, low, close, volume]):
                continue

            df_data.append({
                "high": float(high),
                "low": float(low),
                "close": float(close),
                "volume": float(volume)
            })

        if not df_data:
            raise ValueError("No valid price data available for calculation")

        df = pd.DataFrame(df_data)
        
        # Calculate individual momentum indicators
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        macd_line = ema_12 - ema_26
        macd_signal = macd_line.ewm(span=9).mean()
        macd_histogram = macd_line - macd_signal
        
        # Stochastic
        low_14 = df['low'].rolling(window=14).min()
        high_14 = df['high'].rolling(window=14).max()
        stoch_k = 100 * ((df['close'] - low_14) / (high_14 - low_14))
        stoch_d = stoch_k.rolling(window=3).mean()
        
        # Normalize indicators to 0-100 scale
        rsi_norm = rsi
        macd_norm = ((macd_histogram - macd_histogram.rolling(50).min()) / 
                    (macd_histogram.rolling(50).max() - macd_histogram.rolling(50).min())) * 100
        stoch_norm = stoch_k
        
        # Calculate composite momentum
        composite = (rsi_norm + macd_norm.fillna(50) + stoch_norm) / 3
        
        # Current values
        current_composite = float(composite.iloc[-1])
        current_rsi = float(rsi.iloc[-1])
        current_macd_hist = float(macd_histogram.iloc[-1])
        current_stoch = float(stoch_k.iloc[-1])
        
        # Determine signal strength
        if current_composite > 70:
            signal = "STRONG_BULLISH"
            strength = "HIGH"
        elif current_composite > 60:
            signal = "BULLISH"
            strength = "MODERATE"
        elif current_composite < 30:
            signal = "STRONG_BEARISH"
            strength = "HIGH"
        elif current_composite < 40:
            signal = "BEARISH"
            strength = "MODERATE"
        else:
            signal = "NEUTRAL"
            strength = "LOW"
        
        return {
            "symbol": symbol,
            "interval": interval,
            "indicator": "COMPOSITE_MOMENTUM",
            "current_value": round(current_composite, 2),
            "signal": signal,
            "strength": strength,
            "components": {
                "rsi": round(current_rsi, 2),
                "macd_histogram": round(current_macd_hist, 4),
                "stochastic": round(current_stoch, 2)
            },
            "interpretation": {
                "condition": "OVERBOUGHT" if current_composite > 70 else 
                           "OVERSOLD" if current_composite < 30 else "NEUTRAL",
                "trend": "BULLISH" if current_composite > 50 else "BEARISH",
                "momentum": "ACCELERATING" if composite.diff().iloc[-1] > 0 else "DECELERATING"
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating composite momentum: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate composite momentum: {str(e)}"
        )


def identify_support_resistance(symbol: str, interval: str = "1D", n_bars: int = 200) -> Dict[str, Any]:
    """
    Identify key support and resistance levels using pivot points and price clusters.
    
    This function analyzes historical price data to identify significant levels
    where price has repeatedly found support or resistance.
    
    Args:
        symbol (str): Trading symbol
        interval (str): Time interval
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: Support and resistance levels with strength ratings
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"]
            })
        
        df = pd.DataFrame(df_data)
        
        # Find pivot highs and lows
        pivot_highs = []
        pivot_lows = []
        
        for i in range(2, len(df) - 2):
            # Pivot high: current high is higher than 2 bars before and after
            if (df['high'].iloc[i] > df['high'].iloc[i-1] and 
                df['high'].iloc[i] > df['high'].iloc[i-2] and
                df['high'].iloc[i] > df['high'].iloc[i+1] and 
                df['high'].iloc[i] > df['high'].iloc[i+2]):
                pivot_highs.append(df['high'].iloc[i])
            
            # Pivot low: current low is lower than 2 bars before and after
            if (df['low'].iloc[i] < df['low'].iloc[i-1] and 
                df['low'].iloc[i] < df['low'].iloc[i-2] and
                df['low'].iloc[i] < df['low'].iloc[i+1] and 
                df['low'].iloc[i] < df['low'].iloc[i+2]):
                pivot_lows.append(df['low'].iloc[i])
        
        # Cluster similar levels
        resistance_levels = _cluster_levels(pivot_highs, tolerance=0.01)
        support_levels = _cluster_levels(pivot_lows, tolerance=0.01)
        
        # Current price for context
        current_price = float(df['close'].iloc[-1])
        
        # Filter levels near current price (within 10%)
        nearby_resistance = [level for level in resistance_levels 
                           if level['price'] > current_price and 
                           level['price'] < current_price * 1.1]
        nearby_support = [level for level in support_levels 
                         if level['price'] < current_price and 
                         level['price'] > current_price * 0.9]
        
        # Sort by proximity to current price
        nearby_resistance.sort(key=lambda x: x['price'])
        nearby_support.sort(key=lambda x: x['price'], reverse=True)
        
        return {
            "symbol": symbol,
            "interval": interval,
            "analysis_type": "SUPPORT_RESISTANCE",
            "current_price": current_price,
            "resistance_levels": nearby_resistance[:3],  # Top 3 resistance levels
            "support_levels": nearby_support[:3],        # Top 3 support levels
            "interpretation": {
                "nearest_resistance": nearby_resistance[0]['price'] if nearby_resistance else None,
                "nearest_support": nearby_support[0]['price'] if nearby_support else None,
                "resistance_distance": round(((nearby_resistance[0]['price'] - current_price) / current_price) * 100, 2) if nearby_resistance else None,
                "support_distance": round(((current_price - nearby_support[0]['price']) / current_price) * 100, 2) if nearby_support else None
            }
        }
        
    except Exception as e:
        logger.error(f"Error identifying support/resistance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to identify support/resistance: {str(e)}"
        )


def _cluster_levels(levels: List[float], tolerance: float = 0.01) -> List[Dict[str, Any]]:
    """
    Cluster similar price levels together and calculate their strength.
    
    Args:
        levels (List[float]): List of price levels
        tolerance (float): Tolerance for clustering (as percentage)
        
    Returns:
        List[Dict[str, Any]]: Clustered levels with strength ratings
    """
    if not levels:
        return []
    
    levels = sorted(levels)
    clusters = []
    current_cluster = [levels[0]]
    
    for level in levels[1:]:
        # If level is within tolerance of cluster average, add to cluster
        cluster_avg = sum(current_cluster) / len(current_cluster)
        if abs(level - cluster_avg) / cluster_avg <= tolerance:
            current_cluster.append(level)
        else:
            # Finalize current cluster and start new one
            if len(current_cluster) >= 2:  # Only consider levels hit multiple times
                cluster_price = sum(current_cluster) / len(current_cluster)
                strength = min(100, len(current_cluster) * 20)  # Strength based on touches
                clusters.append({
                    "price": round(cluster_price, 2),
                    "touches": len(current_cluster),
                    "strength": strength
                })
            current_cluster = [level]
    
    # Don't forget the last cluster
    if len(current_cluster) >= 2:
        cluster_price = sum(current_cluster) / len(current_cluster)
        strength = min(100, len(current_cluster) * 20)
        clusters.append({
            "price": round(cluster_price, 2),
            "touches": len(current_cluster),
            "strength": strength
        })
    
    # Sort by strength
    clusters.sort(key=lambda x: x['strength'], reverse=True)
    return clusters


def calculate_trend_strength(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Calculate trend strength using multiple trend indicators.
    
    This function combines ADX, moving average slopes, and price momentum
    to provide a comprehensive trend strength analysis.
    
    Args:
        symbol (str): Trading symbol
        interval (str): Time interval
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: Trend strength analysis
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"]
            })
        
        df = pd.DataFrame(df_data)
        
        # Calculate moving averages
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['ema_20'] = df['close'].ewm(span=20).mean()
        
        # Calculate ADX for trend strength
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        
        plus_dm = np.where((df['high'] - df['high'].shift()) > (df['low'].shift() - df['low']),
                          np.maximum(df['high'] - df['high'].shift(), 0), 0)
        minus_dm = np.where((df['low'].shift() - df['low']) > (df['high'] - df['high'].shift()),
                           np.maximum(df['low'].shift() - df['low'], 0), 0)
        
        tr_14 = pd.Series(true_range).rolling(window=14).mean()
        plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / tr_14)
        minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / tr_14)
        
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=14).mean()
        
        # Calculate moving average slopes (with None handling)
        sma_20_slope = 0
        sma_50_slope = 0

        try:
            if not pd.isna(df['sma_20'].iloc[-1]) and not pd.isna(df['sma_20'].iloc[-10]):
                sma_20_slope = (df['sma_20'].iloc[-1] - df['sma_20'].iloc[-10]) / 10
        except (IndexError, TypeError):
            sma_20_slope = 0

        try:
            if not pd.isna(df['sma_50'].iloc[-1]) and not pd.isna(df['sma_50'].iloc[-10]):
                sma_50_slope = (df['sma_50'].iloc[-1] - df['sma_50'].iloc[-10]) / 10
        except (IndexError, TypeError):
            sma_50_slope = 0
        
        # Current values
        current_adx = float(adx.iloc[-1]) if not pd.isna(adx.iloc[-1]) else 0
        current_price = float(df['close'].iloc[-1])
        current_sma_20 = float(df['sma_20'].iloc[-1])
        current_sma_50 = float(df['sma_50'].iloc[-1])
        
        # Determine trend direction
        if current_price > current_sma_20 > current_sma_50:
            trend_direction = "BULLISH"
        elif current_price < current_sma_20 < current_sma_50:
            trend_direction = "BEARISH"
        else:
            trend_direction = "SIDEWAYS"
        
        # Determine trend strength
        if current_adx > 40:
            strength_rating = "VERY_STRONG"
        elif current_adx > 25:
            strength_rating = "STRONG"
        elif current_adx > 15:
            strength_rating = "MODERATE"
        else:
            strength_rating = "WEAK"
        
        # Calculate composite trend score
        trend_score = 0
        if trend_direction == "BULLISH":
            trend_score += 25
        elif trend_direction == "BEARISH":
            trend_score -= 25
        
        trend_score += min(25, current_adx * 0.625)  # ADX contribution (max 25)
        
        if sma_20_slope > 0:
            trend_score += 15
        else:
            trend_score -= 15
        
        if sma_50_slope > 0:
            trend_score += 10
        else:
            trend_score -= 10
        
        # Normalize to 0-100 scale
        trend_score = max(0, min(100, trend_score + 50))
        
        return {
            "symbol": symbol,
            "interval": interval,
            "indicator": "TREND_STRENGTH",
            "trend_direction": trend_direction,
            "strength_rating": strength_rating,
            "adx_value": round(current_adx, 2),
            "trend_score": round(trend_score, 1),
            "moving_averages": {
                "sma_20": round(current_sma_20, 2),
                "sma_50": round(current_sma_50, 2),
                "sma_20_slope": round(sma_20_slope, 4),
                "sma_50_slope": round(sma_50_slope, 4)
            },
            "interpretation": {
                "signal": "BUY" if trend_direction == "BULLISH" and current_adx > 25 else
                         "SELL" if trend_direction == "BEARISH" and current_adx > 25 else "HOLD",
                "confidence": "HIGH" if current_adx > 30 else "MODERATE" if current_adx > 20 else "LOW"
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating trend strength: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate trend strength: {str(e)}"
        )
