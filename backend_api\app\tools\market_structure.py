"""
Market Structure Analysis Tools for TradingIA Backend.

This module provides market structure analysis including Higher Highs/Lower Lows,
swing points, trend structure analysis, and market phases identification.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from fastapi import HTTPException, status
import logging
from .tradingview_provider import get_price_data

logger = logging.getLogger(__name__)

def analyze_market_structure(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Analyze market structure including swing points, trend structure, and market phases.
    
    This function identifies key market structure elements that professional traders
    use to understand market behavior and make trading decisions.
    
    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA")
        interval (str): Time interval for the data
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: Comprehensive market structure analysis
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"],
                "datetime": bar["datetime"]
            })
        
        df = pd.DataFrame(df_data)
        df.reset_index(drop=True, inplace=True)
        
        # Identify swing points
        swing_highs, swing_lows = _identify_swing_points(df)
        
        # Analyze trend structure
        trend_structure = _analyze_trend_structure(swing_highs, swing_lows)
        
        # Identify market phases
        market_phase = _identify_market_phase(df, swing_highs, swing_lows)
        
        # Calculate key levels
        key_levels = _calculate_key_levels(df, swing_highs, swing_lows)
        
        # Current market position
        current_price = float(df['close'].iloc[-1])
        
        return {
            "symbol": symbol,
            "interval": interval,
            "analysis_type": "MARKET_STRUCTURE",
            "current_price": current_price,
            "trend_structure": trend_structure,
            "market_phase": market_phase,
            "swing_points": {
                "recent_swing_highs": swing_highs[-3:] if len(swing_highs) >= 3 else swing_highs,
                "recent_swing_lows": swing_lows[-3:] if len(swing_lows) >= 3 else swing_lows
            },
            "key_levels": key_levels,
            "interpretation": _interpret_market_structure(trend_structure, market_phase, current_price, key_levels)
        }
        
    except Exception as e:
        logger.error(f"Error analyzing market structure: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze market structure: {str(e)}"
        )


def _identify_swing_points(df: pd.DataFrame, lookback: int = 5) -> Tuple[List[Dict], List[Dict]]:
    """Identify swing highs and swing lows in the price data."""
    swing_highs = []
    swing_lows = []
    
    for i in range(lookback, len(df) - lookback):
        # Check for swing high
        is_swing_high = True
        for j in range(i - lookback, i + lookback + 1):
            if j != i and df['high'].iloc[j] >= df['high'].iloc[i]:
                is_swing_high = False
                break
        
        if is_swing_high:
            swing_highs.append({
                "index": i,
                "price": float(df['high'].iloc[i]),
                "datetime": df['datetime'].iloc[i]
            })
        
        # Check for swing low
        is_swing_low = True
        for j in range(i - lookback, i + lookback + 1):
            if j != i and df['low'].iloc[j] <= df['low'].iloc[i]:
                is_swing_low = False
                break
        
        if is_swing_low:
            swing_lows.append({
                "index": i,
                "price": float(df['low'].iloc[i]),
                "datetime": df['datetime'].iloc[i]
            })
    
    return swing_highs, swing_lows


def _analyze_trend_structure(swing_highs: List[Dict], swing_lows: List[Dict]) -> Dict[str, Any]:
    """Analyze the trend structure based on swing points."""
    if len(swing_highs) < 2 or len(swing_lows) < 2:
        return {
            "trend": "INSUFFICIENT_DATA",
            "structure": "UNKNOWN",
            "strength": "UNKNOWN"
        }
    
    # Analyze recent swing highs (last 3)
    recent_highs = swing_highs[-3:] if len(swing_highs) >= 3 else swing_highs
    recent_lows = swing_lows[-3:] if len(swing_lows) >= 3 else swing_lows
    
    # Check for Higher Highs (HH) and Higher Lows (HL)
    higher_highs = 0
    lower_highs = 0
    for i in range(1, len(recent_highs)):
        if recent_highs[i]["price"] > recent_highs[i-1]["price"]:
            higher_highs += 1
        else:
            lower_highs += 1
    
    # Check for Higher Lows (HL) and Lower Lows (LL)
    higher_lows = 0
    lower_lows = 0
    for i in range(1, len(recent_lows)):
        if recent_lows[i]["price"] > recent_lows[i-1]["price"]:
            higher_lows += 1
        else:
            lower_lows += 1
    
    # Determine trend structure
    if higher_highs > 0 and higher_lows > 0 and lower_highs == 0 and lower_lows == 0:
        trend = "STRONG_UPTREND"
        structure = "HH_HL"
        strength = "STRONG"
    elif higher_highs > lower_highs and higher_lows > lower_lows:
        trend = "UPTREND"
        structure = "HH_HL"
        strength = "MODERATE"
    elif lower_highs > 0 and lower_lows > 0 and higher_highs == 0 and higher_lows == 0:
        trend = "STRONG_DOWNTREND"
        structure = "LH_LL"
        strength = "STRONG"
    elif lower_highs > higher_highs and lower_lows > higher_lows:
        trend = "DOWNTREND"
        structure = "LH_LL"
        strength = "MODERATE"
    else:
        trend = "SIDEWAYS"
        structure = "MIXED"
        strength = "WEAK"
    
    return {
        "trend": trend,
        "structure": structure,
        "strength": strength,
        "higher_highs": higher_highs,
        "lower_highs": lower_highs,
        "higher_lows": higher_lows,
        "lower_lows": lower_lows
    }


def _identify_market_phase(df: pd.DataFrame, swing_highs: List[Dict], swing_lows: List[Dict]) -> Dict[str, Any]:
    """Identify the current market phase (accumulation, markup, distribution, markdown)."""
    if len(df) < 50:
        return {"phase": "INSUFFICIENT_DATA", "confidence": 0}
    
    # Calculate volatility and volume metrics
    df['returns'] = df['close'].pct_change()
    volatility = df['returns'].tail(20).std() * np.sqrt(252)  # Annualized volatility
    
    # Price range analysis
    recent_data = df.tail(20)
    price_range = (recent_data['high'].max() - recent_data['low'].min()) / recent_data['close'].mean()
    
    # Trend analysis
    sma_20 = df['close'].rolling(window=20).mean()
    sma_50 = df['close'].rolling(window=50).mean()
    
    current_price = df['close'].iloc[-1]
    sma_20_current = sma_20.iloc[-1]
    sma_50_current = sma_50.iloc[-1]
    
    # Phase identification logic
    if current_price > sma_20_current > sma_50_current and volatility < 0.3:
        if price_range < 0.05:  # Low volatility, tight range
            phase = "ACCUMULATION"
            confidence = 75
        else:
            phase = "MARKUP"
            confidence = 80
    elif current_price < sma_20_current < sma_50_current and volatility < 0.3:
        if price_range < 0.05:  # Low volatility, tight range
            phase = "DISTRIBUTION"
            confidence = 75
        else:
            phase = "MARKDOWN"
            confidence = 80
    elif price_range < 0.03 and volatility < 0.2:
        # Very tight range, low volatility
        if current_price > sma_50_current:
            phase = "ACCUMULATION"
        else:
            phase = "DISTRIBUTION"
        confidence = 60
    else:
        phase = "TRANSITION"
        confidence = 40
    
    return {
        "phase": phase,
        "confidence": confidence,
        "volatility": round(volatility, 3),
        "price_range": round(price_range, 3),
        "characteristics": _get_phase_characteristics(phase)
    }


def _get_phase_characteristics(phase: str) -> Dict[str, str]:
    """Get characteristics of each market phase."""
    characteristics = {
        "ACCUMULATION": {
            "description": "Smart money accumulating, low volatility, sideways movement",
            "typical_action": "Prepare for potential upward breakout",
            "volume_pattern": "Decreasing on declines, increasing on advances"
        },
        "MARKUP": {
            "description": "Strong upward movement, increasing participation",
            "typical_action": "Trend following strategies work well",
            "volume_pattern": "Increasing volume on advances"
        },
        "DISTRIBUTION": {
            "description": "Smart money distributing, high prices, sideways movement",
            "typical_action": "Prepare for potential downward breakout",
            "volume_pattern": "Increasing on declines, decreasing on advances"
        },
        "MARKDOWN": {
            "description": "Strong downward movement, panic selling",
            "typical_action": "Avoid long positions, consider shorts",
            "volume_pattern": "High volume on declines"
        },
        "TRANSITION": {
            "description": "Market changing phases, mixed signals",
            "typical_action": "Wait for clearer direction",
            "volume_pattern": "Irregular volume patterns"
        }
    }
    
    return characteristics.get(phase, {
        "description": "Unknown phase",
        "typical_action": "Analyze further",
        "volume_pattern": "Unknown"
    })


def _calculate_key_levels(df: pd.DataFrame, swing_highs: List[Dict], swing_lows: List[Dict]) -> Dict[str, Any]:
    """Calculate key support and resistance levels."""
    current_price = float(df['close'].iloc[-1])
    
    # Recent swing levels
    recent_swing_high = max(swing_highs[-3:], key=lambda x: x["price"])["price"] if len(swing_highs) >= 3 else None
    recent_swing_low = min(swing_lows[-3:], key=lambda x: x["price"])["price"] if len(swing_lows) >= 3 else None
    
    # Previous day/period high and low
    prev_high = float(df['high'].iloc[-2]) if len(df) > 1 else current_price
    prev_low = float(df['low'].iloc[-2]) if len(df) > 1 else current_price
    
    # Calculate pivot levels (simple pivot point)
    pivot = (prev_high + prev_low + df['close'].iloc[-2]) / 3 if len(df) > 1 else current_price
    r1 = 2 * pivot - prev_low
    s1 = 2 * pivot - prev_high
    r2 = pivot + (prev_high - prev_low)
    s2 = pivot - (prev_high - prev_low)
    
    return {
        "current_price": current_price,
        "recent_swing_high": recent_swing_high,
        "recent_swing_low": recent_swing_low,
        "pivot_levels": {
            "pivot": round(pivot, 2),
            "resistance_1": round(r1, 2),
            "support_1": round(s1, 2),
            "resistance_2": round(r2, 2),
            "support_2": round(s2, 2)
        },
        "previous_levels": {
            "prev_high": prev_high,
            "prev_low": prev_low
        }
    }


def _interpret_market_structure(
    trend_structure: Dict[str, Any], 
    market_phase: Dict[str, Any], 
    current_price: float, 
    key_levels: Dict[str, Any]
) -> Dict[str, Any]:
    """Interpret the overall market structure for trading decisions."""
    
    trend = trend_structure.get("trend", "UNKNOWN")
    phase = market_phase.get("phase", "UNKNOWN")
    phase_confidence = market_phase.get("confidence", 0)
    
    # Generate overall assessment
    if trend in ["STRONG_UPTREND", "UPTREND"] and phase in ["ACCUMULATION", "MARKUP"]:
        overall_bias = "BULLISH"
        confidence = "HIGH" if phase_confidence > 70 else "MODERATE"
        recommendation = "Look for long opportunities on pullbacks"
    elif trend in ["STRONG_DOWNTREND", "DOWNTREND"] and phase in ["DISTRIBUTION", "MARKDOWN"]:
        overall_bias = "BEARISH"
        confidence = "HIGH" if phase_confidence > 70 else "MODERATE"
        recommendation = "Look for short opportunities on rallies"
    elif phase == "ACCUMULATION":
        overall_bias = "NEUTRAL_TO_BULLISH"
        confidence = "MODERATE"
        recommendation = "Prepare for potential upward breakout"
    elif phase == "DISTRIBUTION":
        overall_bias = "NEUTRAL_TO_BEARISH"
        confidence = "MODERATE"
        recommendation = "Prepare for potential downward breakout"
    else:
        overall_bias = "NEUTRAL"
        confidence = "LOW"
        recommendation = "Wait for clearer market structure"
    
    # Key level analysis
    pivot = key_levels["pivot_levels"]["pivot"]
    r1 = key_levels["pivot_levels"]["resistance_1"]
    s1 = key_levels["pivot_levels"]["support_1"]
    
    if current_price > pivot:
        level_bias = "ABOVE_PIVOT"
        next_target = r1
        key_support = pivot
    else:
        level_bias = "BELOW_PIVOT"
        next_target = s1
        key_support = s1
    
    return {
        "overall_bias": overall_bias,
        "confidence": confidence,
        "recommendation": recommendation,
        "level_analysis": {
            "position": level_bias,
            "next_target": round(next_target, 2),
            "key_support": round(key_support, 2),
            "target_distance": round(abs(next_target - current_price) / current_price * 100, 2)
        },
        "risk_assessment": {
            "trend_strength": trend_structure.get("strength", "UNKNOWN"),
            "phase_confidence": phase_confidence,
            "structure_quality": "GOOD" if trend_structure.get("strength") in ["STRONG", "MODERATE"] else "POOR"
        }
    }
