"""
Multi-Timeframe Analysis Tools for TradingIA Backend.

This module provides multi-timeframe analysis capabilities to align
trends across different time periods and provide comprehensive market context.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from fastapi import HTTPException, status
import logging
from .tradingview_provider import get_price_data, apply_indicator

logger = logging.getLogger(__name__)

def get_multi_timeframe_analysis(
    symbol: str, 
    timeframes: List[str] = ["1W", "1D", "4h"], 
    n_bars: int = 100
) -> Dict[str, Any]:
    """
    Perform multi-timeframe analysis to align trends across different periods.
    
    This function analyzes the same asset across multiple timeframes to provide
    a comprehensive view of the trend structure and identify confluences.
    
    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA")
        timeframes (List[str]): List of timeframes to analyze
        n_bars (int): Number of bars to analyze for each timeframe
        
    Returns:
        Dict[str, Any]: Multi-timeframe analysis with trend alignment
    """
    try:
        analysis_results = {}
        trend_alignment = {}
        
        for timeframe in timeframes:
            try:
                # Get price data for this timeframe
                price_data = get_price_data(symbol, timeframe, n_bars)
                
                # Calculate key indicators for trend analysis
                sma_20 = apply_indicator(symbol, timeframe, "SMA", {"length": 20})
                sma_50 = apply_indicator(symbol, timeframe, "SMA", {"length": 50})
                rsi = apply_indicator(symbol, timeframe, "RSI", {"length": 14})
                macd = apply_indicator(symbol, timeframe, "MACD", {"fast": 12, "slow": 26, "signal": 9})
                
                # Analyze trend for this timeframe
                timeframe_analysis = _analyze_timeframe_trend(
                    price_data, sma_20, sma_50, rsi, macd, timeframe
                )
                
                analysis_results[timeframe] = timeframe_analysis
                trend_alignment[timeframe] = timeframe_analysis["trend_direction"]
                
            except Exception as e:
                logger.warning(f"Failed to analyze timeframe {timeframe}: {e}")
                analysis_results[timeframe] = {"error": str(e)}
                trend_alignment[timeframe] = "UNKNOWN"
        
        # Calculate overall trend alignment
        overall_analysis = _calculate_trend_confluence(trend_alignment, analysis_results)
        
        return {
            "symbol": symbol,
            "analysis_type": "MULTI_TIMEFRAME",
            "timeframes_analyzed": timeframes,
            "individual_analysis": analysis_results,
            "trend_alignment": trend_alignment,
            "overall_analysis": overall_analysis,
            "recommendation": _get_multi_timeframe_recommendation(overall_analysis, analysis_results)
        }
        
    except Exception as e:
        logger.error(f"Error in multi-timeframe analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform multi-timeframe analysis: {str(e)}"
        )


def _analyze_timeframe_trend(
    price_data: Dict[str, Any],
    sma_20: Dict[str, Any],
    sma_50: Dict[str, Any],
    rsi: Dict[str, Any],
    macd: Dict[str, Any],
    timeframe: str
) -> Dict[str, Any]:
    """Analyze trend for a specific timeframe."""
    try:
        # Get current price and moving averages
        current_price = price_data.get("latest_price", 0)
        
        # Get latest indicator values - handle different response formats
        sma_20_current = 0
        if sma_20 and "values" in sma_20 and sma_20["values"]:
            if isinstance(sma_20["values"][-1], dict) and "sma" in sma_20["values"][-1]:
                sma_20_current = sma_20["values"][-1]["sma"]
            elif isinstance(sma_20["values"][-1], (int, float)):
                sma_20_current = sma_20["values"][-1]

        sma_50_current = 0
        if sma_50 and "values" in sma_50 and sma_50["values"]:
            if isinstance(sma_50["values"][-1], dict) and "sma" in sma_50["values"][-1]:
                sma_50_current = sma_50["values"][-1]["sma"]
            elif isinstance(sma_50["values"][-1], (int, float)):
                sma_50_current = sma_50["values"][-1]

        rsi_current = 50
        if rsi and "values" in rsi and rsi["values"]:
            if isinstance(rsi["values"][-1], dict) and "rsi" in rsi["values"][-1]:
                rsi_current = rsi["values"][-1]["rsi"]
            elif isinstance(rsi["values"][-1], (int, float)):
                rsi_current = rsi["values"][-1]
        
        # MACD analysis - handle different response formats
        macd_line = 0
        macd_signal = 0
        macd_histogram = 0

        if macd and "values" in macd and macd["values"]:
            macd_values = macd["values"][-1]
            if isinstance(macd_values, dict):
                macd_line = macd_values.get("macd", 0)
                macd_signal = macd_values.get("signal", 0)
                macd_histogram = macd_values.get("histogram", 0)
            elif isinstance(macd_values, (int, float)):
                macd_line = macd_values
        
        # Determine trend direction
        price_vs_sma20 = "ABOVE" if current_price > sma_20_current else "BELOW"
        price_vs_sma50 = "ABOVE" if current_price > sma_50_current else "BELOW"
        sma_alignment = "BULLISH" if sma_20_current > sma_50_current else "BEARISH"
        
        # Overall trend determination
        bullish_signals = 0
        bearish_signals = 0
        
        if price_vs_sma20 == "ABOVE":
            bullish_signals += 1
        else:
            bearish_signals += 1
            
        if price_vs_sma50 == "ABOVE":
            bullish_signals += 1
        else:
            bearish_signals += 1
            
        if sma_alignment == "BULLISH":
            bullish_signals += 1
        else:
            bearish_signals += 1
            
        if macd_line > macd_signal:
            bullish_signals += 1
        else:
            bearish_signals += 1
        
        # Determine overall trend
        if bullish_signals >= 3:
            trend_direction = "BULLISH"
            trend_strength = "STRONG" if bullish_signals == 4 else "MODERATE"
        elif bearish_signals >= 3:
            trend_direction = "BEARISH"
            trend_strength = "STRONG" if bearish_signals == 4 else "MODERATE"
        else:
            trend_direction = "NEUTRAL"
            trend_strength = "WEAK"
        
        # Calculate momentum
        momentum = "POSITIVE" if macd_histogram > 0 else "NEGATIVE"
        
        return {
            "timeframe": timeframe,
            "trend_direction": trend_direction,
            "trend_strength": trend_strength,
            "momentum": momentum,
            "current_price": current_price,
            "sma_20": sma_20_current,
            "sma_50": sma_50_current,
            "rsi": rsi_current,
            "macd_signal": "BULLISH" if macd_line > macd_signal else "BEARISH",
            "price_position": {
                "vs_sma20": price_vs_sma20,
                "vs_sma50": price_vs_sma50
            },
            "technical_score": bullish_signals - bearish_signals
        }
        
    except Exception as e:
        logger.error(f"Error analyzing timeframe {timeframe}: {e}")
        return {
            "timeframe": timeframe,
            "trend_direction": "UNKNOWN",
            "error": str(e)
        }


def _calculate_trend_confluence(
    trend_alignment: Dict[str, str], 
    analysis_results: Dict[str, Any]
) -> Dict[str, Any]:
    """Calculate overall trend confluence across timeframes."""
    try:
        # Count trend directions
        bullish_count = sum(1 for trend in trend_alignment.values() if trend == "BULLISH")
        bearish_count = sum(1 for trend in trend_alignment.values() if trend == "BEARISH")
        neutral_count = sum(1 for trend in trend_alignment.values() if trend == "NEUTRAL")
        total_timeframes = len(trend_alignment)
        
        # Calculate confluence percentage
        if bullish_count > bearish_count:
            confluence_direction = "BULLISH"
            confluence_percentage = (bullish_count / total_timeframes) * 100
        elif bearish_count > bullish_count:
            confluence_direction = "BEARISH"
            confluence_percentage = (bearish_count / total_timeframes) * 100
        else:
            confluence_direction = "NEUTRAL"
            confluence_percentage = (neutral_count / total_timeframes) * 100
        
        # Determine confluence strength
        if confluence_percentage >= 75:
            confluence_strength = "VERY_STRONG"
        elif confluence_percentage >= 60:
            confluence_strength = "STRONG"
        elif confluence_percentage >= 40:
            confluence_strength = "MODERATE"
        else:
            confluence_strength = "WEAK"
        
        # Calculate average technical score
        technical_scores = [
            analysis.get("technical_score", 0) 
            for analysis in analysis_results.values() 
            if "technical_score" in analysis
        ]
        avg_technical_score = sum(technical_scores) / len(technical_scores) if technical_scores else 0
        
        return {
            "confluence_direction": confluence_direction,
            "confluence_percentage": round(confluence_percentage, 1),
            "confluence_strength": confluence_strength,
            "bullish_timeframes": bullish_count,
            "bearish_timeframes": bearish_count,
            "neutral_timeframes": neutral_count,
            "average_technical_score": round(avg_technical_score, 2),
            "alignment_quality": "HIGH" if confluence_percentage >= 75 else 
                               "MEDIUM" if confluence_percentage >= 50 else "LOW"
        }
        
    except Exception as e:
        logger.error(f"Error calculating trend confluence: {e}")
        return {
            "confluence_direction": "UNKNOWN",
            "error": str(e)
        }


def _get_multi_timeframe_recommendation(
    overall_analysis: Dict[str, Any], 
    analysis_results: Dict[str, Any]
) -> Dict[str, Any]:
    """Generate trading recommendation based on multi-timeframe analysis."""
    try:
        confluence_direction = overall_analysis.get("confluence_direction", "NEUTRAL")
        confluence_strength = overall_analysis.get("confluence_strength", "WEAK")
        confluence_percentage = overall_analysis.get("confluence_percentage", 0)
        
        # Base recommendation on confluence
        if confluence_direction == "BULLISH" and confluence_percentage >= 60:
            action = "BUY"
            confidence = min(95, 50 + confluence_percentage * 0.5)
            reason = f"Bullish confluence across {confluence_percentage}% of timeframes"
        elif confluence_direction == "BEARISH" and confluence_percentage >= 60:
            action = "SELL"
            confidence = min(95, 50 + confluence_percentage * 0.5)
            reason = f"Bearish confluence across {confluence_percentage}% of timeframes"
        else:
            action = "HOLD"
            confidence = 50
            reason = "Mixed signals across timeframes - wait for clearer direction"
        
        # Adjust confidence based on strength
        if confluence_strength == "VERY_STRONG":
            confidence = min(95, confidence + 10)
        elif confluence_strength == "WEAK":
            confidence = max(30, confidence - 15)
        
        # Check for divergences (warning signals)
        warnings = []
        timeframe_list = list(analysis_results.keys())
        
        if len(timeframe_list) >= 2:
            # Check if higher timeframe contradicts lower timeframe
            higher_tf = analysis_results.get(timeframe_list[0], {})
            lower_tf = analysis_results.get(timeframe_list[-1], {})
            
            higher_trend = higher_tf.get("trend_direction", "UNKNOWN")
            lower_trend = lower_tf.get("trend_direction", "UNKNOWN")
            
            if higher_trend != lower_trend and higher_trend != "UNKNOWN" and lower_trend != "UNKNOWN":
                warnings.append(f"Divergence: {timeframe_list[0]} is {higher_trend} while {timeframe_list[-1]} is {lower_trend}")
        
        return {
            "action": action,
            "confidence": round(confidence, 1),
            "reason": reason,
            "confluence_strength": confluence_strength,
            "warnings": warnings,
            "best_entry_timeframe": _get_best_entry_timeframe(analysis_results, confluence_direction)
        }
        
    except Exception as e:
        logger.error(f"Error generating multi-timeframe recommendation: {e}")
        return {
            "action": "HOLD",
            "confidence": 0,
            "reason": "Error in analysis",
            "error": str(e)
        }


def _get_best_entry_timeframe(analysis_results: Dict[str, Any], confluence_direction: str) -> str:
    """Determine the best timeframe for entry based on momentum and trend alignment."""
    try:
        best_timeframe = None
        best_score = -999
        
        for timeframe, analysis in analysis_results.items():
            if "error" in analysis:
                continue
                
            trend_direction = analysis.get("trend_direction", "NEUTRAL")
            momentum = analysis.get("momentum", "NEUTRAL")
            technical_score = analysis.get("technical_score", 0)
            
            # Score based on alignment with confluence direction
            score = 0
            if trend_direction == confluence_direction:
                score += 3
            
            if momentum == "POSITIVE" and confluence_direction == "BULLISH":
                score += 2
            elif momentum == "NEGATIVE" and confluence_direction == "BEARISH":
                score += 2
            
            score += technical_score
            
            if score > best_score:
                best_score = score
                best_timeframe = timeframe
        
        return best_timeframe or "1D"
        
    except Exception:
        return "1D"


def get_timeframe_context(symbol: str, primary_timeframe: str = "1D") -> Dict[str, Any]:
    """
    Get context from higher and lower timeframes for the primary timeframe.
    
    Args:
        symbol (str): Trading symbol
        primary_timeframe (str): Primary timeframe for analysis
        
    Returns:
        Dict[str, Any]: Context from surrounding timeframes
    """
    try:
        # Define timeframe hierarchy
        timeframe_hierarchy = ["1M", "1W", "1D", "4h", "1h", "15m", "5m", "1m"]
        
        if primary_timeframe not in timeframe_hierarchy:
            primary_timeframe = "1D"
        
        primary_index = timeframe_hierarchy.index(primary_timeframe)
        
        # Get higher and lower timeframes
        higher_timeframes = timeframe_hierarchy[:primary_index]
        lower_timeframes = timeframe_hierarchy[primary_index + 1:]
        
        # Select one higher and one lower timeframe
        higher_tf = higher_timeframes[-1] if higher_timeframes else None
        lower_tf = lower_timeframes[0] if lower_timeframes else None
        
        context = {
            "primary_timeframe": primary_timeframe,
            "higher_timeframe": higher_tf,
            "lower_timeframe": lower_tf
        }
        
        # Analyze each timeframe
        for tf_key, tf_value in context.items():
            if tf_value and tf_key != "primary_timeframe":
                try:
                    price_data = get_price_data(symbol, tf_value, 50)
                    sma_20 = apply_indicator(symbol, tf_value, "SMA", {"length": 20})
                    
                    current_price = price_data.get("latest_price", 0)

                    # Handle different SMA response formats
                    sma_20_current = 0
                    if sma_20 and "values" in sma_20 and sma_20["values"]:
                        if isinstance(sma_20["values"][-1], dict) and "sma" in sma_20["values"][-1]:
                            sma_20_current = sma_20["values"][-1]["sma"]
                        elif isinstance(sma_20["values"][-1], (int, float)):
                            sma_20_current = sma_20["values"][-1]
                    
                    trend = "BULLISH" if current_price > sma_20_current else "BEARISH"
                    
                    context[f"{tf_key}_analysis"] = {
                        "trend": trend,
                        "current_price": current_price,
                        "sma_20": sma_20_current
                    }
                    
                except Exception as e:
                    context[f"{tf_key}_analysis"] = {"error": str(e)}
        
        return {
            "symbol": symbol,
            "timeframe_context": context,
            "interpretation": _interpret_timeframe_context(context)
        }
        
    except Exception as e:
        logger.error(f"Error getting timeframe context: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get timeframe context: {str(e)}"
        )


def _interpret_timeframe_context(context: Dict[str, Any]) -> Dict[str, Any]:
    """Interpret the timeframe context for trading decisions."""
    try:
        higher_analysis = context.get("higher_timeframe_analysis", {})
        lower_analysis = context.get("lower_timeframe_analysis", {})
        
        higher_trend = higher_analysis.get("trend", "UNKNOWN")
        lower_trend = lower_analysis.get("trend", "UNKNOWN")
        
        # Determine overall context
        if higher_trend == "BULLISH" and lower_trend == "BULLISH":
            overall_context = "STRONG_BULLISH"
            recommendation = "Look for long entries on pullbacks"
        elif higher_trend == "BEARISH" and lower_trend == "BEARISH":
            overall_context = "STRONG_BEARISH"
            recommendation = "Look for short entries on rallies"
        elif higher_trend == "BULLISH" and lower_trend == "BEARISH":
            overall_context = "BULLISH_PULLBACK"
            recommendation = "Potential buying opportunity if higher timeframe holds"
        elif higher_trend == "BEARISH" and lower_trend == "BULLISH":
            overall_context = "BEARISH_RALLY"
            recommendation = "Potential selling opportunity - rally in downtrend"
        else:
            overall_context = "MIXED"
            recommendation = "Wait for clearer directional bias"
        
        return {
            "overall_context": overall_context,
            "recommendation": recommendation,
            "higher_timeframe_trend": higher_trend,
            "lower_timeframe_trend": lower_trend
        }
        
    except Exception:
        return {
            "overall_context": "UNKNOWN",
            "recommendation": "Unable to determine context"
        }
