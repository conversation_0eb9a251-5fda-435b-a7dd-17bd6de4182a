"""
Pattern Recognition Tools for TradingIA Backend.

This module provides chart pattern recognition capabilities including
triangles, head and shoulders, double tops/bottoms, flags, and wedges.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from fastapi import HTTPException, status
import logging
from scipy.signal import find_peaks
from .tradingview_provider import get_price_data

logger = logging.getLogger(__name__)

def detect_chart_patterns(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Detect classic chart patterns in price data.
    
    This function analyzes price data to identify common chart patterns
    that traders use for technical analysis.
    
    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA")
        interval (str): Time interval for the data
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: Detected patterns with confidence scores
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame with validation
        df_data = []
        for bar in price_data["data"]:
            # Validate and sanitize data
            high = bar.get("high")
            low = bar.get("low")
            close = bar.get("close")
            volume = bar.get("volume")

            # Skip bars with None values
            if any(val is None for val in [high, low, close, volume]):
                continue

            df_data.append({
                "high": float(high),
                "low": float(low),
                "close": float(close),
                "volume": float(volume)
            })

        if not df_data:
            raise ValueError("No valid price data available for pattern detection")

        df = pd.DataFrame(df_data)
        df.reset_index(drop=True, inplace=True)
        
        detected_patterns = []
        
        # Detect various patterns
        patterns = [
            _detect_double_top(df),
            _detect_double_bottom(df),
            _detect_head_shoulders(df),
            _detect_triangle_patterns(df),
            _detect_flag_patterns(df)
        ]
        
        # Filter out None results
        detected_patterns = [p for p in patterns if p is not None]
        
        # Sort by confidence score
        detected_patterns.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        return {
            "symbol": symbol,
            "interval": interval,
            "analysis_type": "CHART_PATTERNS",
            "patterns_detected": len(detected_patterns),
            "patterns": detected_patterns[:3],  # Top 3 patterns
            "interpretation": _interpret_patterns(detected_patterns),
            "recommendation": _get_pattern_recommendation(detected_patterns)
        }
        
    except Exception as e:
        logger.error(f"Error detecting chart patterns: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to detect chart patterns: {str(e)}"
        )


def _detect_double_top(df: pd.DataFrame) -> Optional[Dict[str, Any]]:
    """Detect double top pattern."""
    try:
        if len(df) < 20:
            return None
            
        # Find peaks in the data
        peaks, _ = find_peaks(df['high'], distance=5, prominence=df['high'].std() * 0.5)
        
        if len(peaks) < 2:
            return None
        
        # Look for two similar peaks
        for i in range(len(peaks) - 1):
            peak1_idx, peak2_idx = peaks[i], peaks[i + 1]
            peak1_price, peak2_price = df['high'].iloc[peak1_idx], df['high'].iloc[peak2_idx]
            
            # Check if peaks are similar (within 2%)
            price_diff = abs(peak1_price - peak2_price) / peak1_price
            if price_diff < 0.02 and (peak2_idx - peak1_idx) > 10:
                
                # Find the valley between peaks
                valley_section = df['low'].iloc[peak1_idx:peak2_idx]
                valley_idx = valley_section.idxmin()
                valley_price = df['low'].iloc[valley_idx]
                
                # Calculate pattern metrics
                pattern_height = max(peak1_price, peak2_price) - valley_price
                target_price = valley_price - pattern_height
                
                confidence = min(90, 70 + (1 - price_diff) * 20)
                
                return {
                    "pattern_type": "DOUBLE_TOP",
                    "confidence": round(confidence, 1),
                    "resistance_level": round(max(peak1_price, peak2_price), 2),
                    "support_level": round(valley_price, 2),
                    "target_price": round(target_price, 2),
                    "pattern_status": "FORMING" if peak2_idx > len(df) - 5 else "COMPLETED",
                    "bearish_signal": True
                }
        
        return None
        
    except Exception:
        return None


def _detect_double_bottom(df: pd.DataFrame) -> Optional[Dict[str, Any]]:
    """Detect double bottom pattern."""
    try:
        if len(df) < 20:
            return None
            
        # Find troughs in the data
        troughs, _ = find_peaks(-df['low'], distance=5, prominence=df['low'].std() * 0.5)
        
        if len(troughs) < 2:
            return None
        
        # Look for two similar troughs
        for i in range(len(troughs) - 1):
            trough1_idx, trough2_idx = troughs[i], troughs[i + 1]
            trough1_price, trough2_price = df['low'].iloc[trough1_idx], df['low'].iloc[trough2_idx]
            
            # Check if troughs are similar (within 2%)
            price_diff = abs(trough1_price - trough2_price) / trough1_price
            if price_diff < 0.02 and (trough2_idx - trough1_idx) > 10:
                
                # Find the peak between troughs
                peak_section = df['high'].iloc[trough1_idx:trough2_idx]
                peak_idx = peak_section.idxmax()
                peak_price = df['high'].iloc[peak_idx]
                
                # Calculate pattern metrics
                pattern_height = peak_price - min(trough1_price, trough2_price)
                target_price = peak_price + pattern_height
                
                confidence = min(90, 70 + (1 - price_diff) * 20)
                
                return {
                    "pattern_type": "DOUBLE_BOTTOM",
                    "confidence": round(confidence, 1),
                    "support_level": round(min(trough1_price, trough2_price), 2),
                    "resistance_level": round(peak_price, 2),
                    "target_price": round(target_price, 2),
                    "pattern_status": "FORMING" if trough2_idx > len(df) - 5 else "COMPLETED",
                    "bullish_signal": True
                }
        
        return None
        
    except Exception:
        return None


def _detect_head_shoulders(df: pd.DataFrame) -> Optional[Dict[str, Any]]:
    """Detect head and shoulders pattern."""
    try:
        if len(df) < 30:
            return None
            
        # Find peaks
        peaks, _ = find_peaks(df['high'], distance=5, prominence=df['high'].std() * 0.3)
        
        if len(peaks) < 3:
            return None
        
        # Look for head and shoulders pattern (3 peaks with middle one highest)
        for i in range(len(peaks) - 2):
            left_shoulder = peaks[i]
            head = peaks[i + 1]
            right_shoulder = peaks[i + 2]
            
            left_price = df['high'].iloc[left_shoulder]
            head_price = df['high'].iloc[head]
            right_price = df['high'].iloc[right_shoulder]
            
            # Check if head is higher than shoulders
            if (head_price > left_price and head_price > right_price and
                abs(left_price - right_price) / left_price < 0.05):  # Shoulders similar height
                
                # Find neckline (valleys between shoulders and head)
                left_valley = df['low'].iloc[left_shoulder:head].idxmin()
                right_valley = df['low'].iloc[head:right_shoulder].idxmin()
                
                neckline_price = (df['low'].iloc[left_valley] + df['low'].iloc[right_valley]) / 2
                
                # Calculate target
                pattern_height = head_price - neckline_price
                target_price = neckline_price - pattern_height
                
                # Calculate confidence based on pattern symmetry
                shoulder_symmetry = 1 - abs(left_price - right_price) / left_price
                confidence = min(85, 60 + shoulder_symmetry * 25)
                
                return {
                    "pattern_type": "HEAD_AND_SHOULDERS",
                    "confidence": round(confidence, 1),
                    "head_price": round(head_price, 2),
                    "left_shoulder": round(left_price, 2),
                    "right_shoulder": round(right_price, 2),
                    "neckline": round(neckline_price, 2),
                    "target_price": round(target_price, 2),
                    "pattern_status": "FORMING" if right_shoulder > len(df) - 5 else "COMPLETED",
                    "bearish_signal": True
                }
        
        return None
        
    except Exception:
        return None


def _detect_triangle_patterns(df: pd.DataFrame) -> Optional[Dict[str, Any]]:
    """Detect triangle patterns (ascending, descending, symmetrical)."""
    try:
        if len(df) < 20:
            return None
        
        # Get recent data for triangle analysis
        recent_data = df.tail(20)
        
        # Find peaks and troughs
        peaks, _ = find_peaks(recent_data['high'], distance=3)
        troughs, _ = find_peaks(-recent_data['low'], distance=3)
        
        if len(peaks) < 2 or len(troughs) < 2:
            return None
        
        # Analyze trend lines
        peak_prices = recent_data['high'].iloc[peaks]
        trough_prices = recent_data['low'].iloc[troughs]
        
        # Calculate slopes
        if len(peak_prices) >= 2:
            peak_slope = (peak_prices.iloc[-1] - peak_prices.iloc[0]) / len(peak_prices)
        else:
            peak_slope = 0
            
        if len(trough_prices) >= 2:
            trough_slope = (trough_prices.iloc[-1] - trough_prices.iloc[0]) / len(trough_prices)
        else:
            trough_slope = 0
        
        # Determine triangle type
        if abs(peak_slope) < 0.1 and trough_slope > 0.1:
            triangle_type = "ASCENDING_TRIANGLE"
            bullish_signal = True
            confidence = 75
        elif peak_slope < -0.1 and abs(trough_slope) < 0.1:
            triangle_type = "DESCENDING_TRIANGLE"
            bullish_signal = False
            confidence = 75
        elif abs(peak_slope) < 0.1 and abs(trough_slope) < 0.1:
            triangle_type = "SYMMETRICAL_TRIANGLE"
            bullish_signal = None  # Direction depends on breakout
            confidence = 65
        else:
            return None
        
        # Calculate support and resistance levels
        resistance = float(peak_prices.mean())
        support = float(trough_prices.mean())
        
        return {
            "pattern_type": triangle_type,
            "confidence": confidence,
            "resistance_level": round(resistance, 2),
            "support_level": round(support, 2),
            "pattern_status": "FORMING",
            "bullish_signal": bullish_signal,
            "breakout_target_up": round(resistance + (resistance - support), 2),
            "breakout_target_down": round(support - (resistance - support), 2)
        }
        
    except Exception:
        return None


def _detect_flag_patterns(df: pd.DataFrame) -> Optional[Dict[str, Any]]:
    """Detect flag and pennant patterns."""
    try:
        if len(df) < 15:
            return None
        
        # Look for strong move followed by consolidation
        recent_data = df.tail(15)
        
        # Check for strong initial move (first 5 bars)
        initial_move = recent_data.head(5)
        consolidation = recent_data.tail(10)
        
        # Calculate move strength
        move_start = initial_move['close'].iloc[0]
        move_end = initial_move['close'].iloc[-1]
        move_percent = abs(move_end - move_start) / move_start
        
        # Check if move is significant (>3%)
        if move_percent < 0.03:
            return None
        
        # Check consolidation characteristics
        consolidation_range = consolidation['high'].max() - consolidation['low'].min()
        consolidation_percent = consolidation_range / consolidation['close'].mean()
        
        # Flag pattern: tight consolidation after strong move
        if consolidation_percent < 0.02:  # Tight range
            is_bullish = move_end > move_start
            
            # Calculate target
            flag_height = abs(move_end - move_start)
            current_price = recent_data['close'].iloc[-1]
            target_price = current_price + flag_height if is_bullish else current_price - flag_height
            
            return {
                "pattern_type": "BULL_FLAG" if is_bullish else "BEAR_FLAG",
                "confidence": 70,
                "flag_high": round(consolidation['high'].max(), 2),
                "flag_low": round(consolidation['low'].min(), 2),
                "target_price": round(target_price, 2),
                "pattern_status": "FORMING",
                "bullish_signal": is_bullish
            }
        
        return None
        
    except Exception:
        return None


def _interpret_patterns(patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Interpret the overall signal from detected patterns."""
    if not patterns:
        return {"overall_signal": "NEUTRAL", "confidence": 0}
    
    bullish_patterns = [p for p in patterns if p.get('bullish_signal') is True]
    bearish_patterns = [p for p in patterns if p.get('bullish_signal') is False]
    
    bullish_confidence = sum(p.get('confidence', 0) for p in bullish_patterns)
    bearish_confidence = sum(p.get('confidence', 0) for p in bearish_patterns)
    
    if bullish_confidence > bearish_confidence:
        signal = "BULLISH"
        confidence = bullish_confidence
    elif bearish_confidence > bullish_confidence:
        signal = "BEARISH"
        confidence = bearish_confidence
    else:
        signal = "NEUTRAL"
        confidence = max(bullish_confidence, bearish_confidence)
    
    return {
        "overall_signal": signal,
        "confidence": min(100, confidence),
        "bullish_patterns": len(bullish_patterns),
        "bearish_patterns": len(bearish_patterns)
    }


def _get_pattern_recommendation(patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Get trading recommendation based on detected patterns."""
    if not patterns:
        return {"action": "HOLD", "reason": "No significant patterns detected"}
    
    highest_confidence_pattern = max(patterns, key=lambda x: x.get('confidence', 0))
    
    if highest_confidence_pattern.get('confidence', 0) > 70:
        if highest_confidence_pattern.get('bullish_signal'):
            return {
                "action": "BUY",
                "reason": f"{highest_confidence_pattern['pattern_type']} pattern detected",
                "target": highest_confidence_pattern.get('target_price'),
                "confidence": highest_confidence_pattern.get('confidence')
            }
        elif highest_confidence_pattern.get('bullish_signal') is False:
            return {
                "action": "SELL",
                "reason": f"{highest_confidence_pattern['pattern_type']} pattern detected",
                "target": highest_confidence_pattern.get('target_price'),
                "confidence": highest_confidence_pattern.get('confidence')
            }
    
    return {"action": "HOLD", "reason": "Pattern confidence too low for clear signal"}
