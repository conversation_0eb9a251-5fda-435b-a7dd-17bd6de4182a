"""
Volume Analysis Tools for TradingIA Backend.

This module provides specialized volume analysis tools including VWAP,
OBV, volume trends, and volume confirmation indicators.
"""

import pandas as pd
import numpy as np
import math
from typing import Dict, Any, List, Optional
from fastapi import HTTPException, status
import logging
from .tradingview_provider import get_price_data

logger = logging.getLogger(__name__)

def sanitize_float_value(value):
    """Sanitiza valores float para que sean compatibles con JSON"""
    if value is None:
        return None
    if isinstance(value, (int, str, bool)):
        return value
    if isinstance(value, float):
        if math.isnan(value) or math.isinf(value):
            return None
        return value
    return float(value) if value is not None else None

def calculate_vwap(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Calculate Volume Weighted Average Price (VWAP).
    
    VWAP is a trading benchmark that gives the average price a security has traded at
    throughout the day, based on both volume and price.
    
    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA")
        interval (str): Time interval for the data
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: VWAP values and analysis
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame with validation
        df_data = []
        for bar in price_data["data"]:
            # Validate and sanitize data
            high = bar.get("high")
            low = bar.get("low")
            close = bar.get("close")
            volume = bar.get("volume")

            # Skip bars with None values
            if any(val is None for val in [high, low, close, volume]):
                continue

            df_data.append({
                "high": float(high),
                "low": float(low),
                "close": float(close),
                "volume": float(volume)
            })

        if not df_data:
            raise ValueError("No valid price data available for VWAP calculation")

        df = pd.DataFrame(df_data)
        
        # Calculate typical price
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        
        # Calculate VWAP
        df['volume_price'] = df['typical_price'] * df['volume']
        df['cumulative_volume_price'] = df['volume_price'].cumsum()
        df['cumulative_volume'] = df['volume'].cumsum()
        df['vwap'] = df['cumulative_volume_price'] / df['cumulative_volume']
        
        # Current VWAP and price comparison
        current_vwap = float(df['vwap'].iloc[-1])
        current_price = float(df['close'].iloc[-1])
        price_vs_vwap = ((current_price - current_vwap) / current_vwap) * 100
        
        # VWAP trend analysis
        vwap_values = df['vwap'].tail(10).tolist()
        vwap_trend = "ALCISTA" if vwap_values[-1] > vwap_values[0] else "BAJISTA"
        
        result = {
            "symbol": symbol,
            "interval": interval,
            "indicator": "VWAP",
            "current_vwap": sanitize_float_value(current_vwap),
            "current_price": sanitize_float_value(current_price),
            "price_vs_vwap_percent": sanitize_float_value(round(price_vs_vwap, 2)),
            "vwap_trend": vwap_trend,
            "interpretation": {
                "price_position": "ABOVE_VWAP" if current_price > current_vwap else "BELOW_VWAP",
                "signal": "BULLISH" if current_price > current_vwap and vwap_trend == "ALCISTA" else "BEARISH",
                "strength": "STRONG" if abs(price_vs_vwap) > 2 else "MODERATE" if abs(price_vs_vwap) > 0.5 else "WEAK"
            },
            "values": [{"vwap": sanitize_float_value(val)} for val in vwap_values]
        }

        return result
        
    except Exception as e:
        logger.error(f"Error calculating VWAP: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate VWAP: {str(e)}"
        )


def calculate_obv(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Calculate On-Balance Volume (OBV).
    
    OBV is a momentum indicator that uses volume flow to predict changes in stock price.
    
    Args:
        symbol (str): Trading symbol
        interval (str): Time interval
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: OBV values and trend analysis
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "close": bar["close"],
                "volume": bar["volume"]
            })
        
        df = pd.DataFrame(df_data)
        
        # Calculate OBV
        df['price_change'] = df['close'].diff()
        df['obv'] = 0
        
        for i in range(1, len(df)):
            if df['price_change'].iloc[i] > 0:
                df.loc[i, 'obv'] = df['obv'].iloc[i-1] + df['volume'].iloc[i]
            elif df['price_change'].iloc[i] < 0:
                df.loc[i, 'obv'] = df['obv'].iloc[i-1] - df['volume'].iloc[i]
            else:
                df.loc[i, 'obv'] = df['obv'].iloc[i-1]
        
        # OBV trend analysis
        obv_values = df['obv'].tail(20).tolist()
        obv_sma_short = df['obv'].tail(5).mean()
        obv_sma_long = df['obv'].tail(20).mean()
        
        obv_trend = "ALCISTA" if obv_sma_short > obv_sma_long else "BAJISTA"
        
        # Price vs OBV divergence
        price_trend = "ALCISTA" if df['close'].tail(5).mean() > df['close'].tail(20).mean() else "BAJISTA"
        divergence = "BEARISH_DIVERGENCE" if price_trend == "ALCISTA" and obv_trend == "BAJISTA" else \
                    "BULLISH_DIVERGENCE" if price_trend == "BAJISTA" and obv_trend == "ALCISTA" else \
                    "NO_DIVERGENCE"
        
        return {
            "symbol": symbol,
            "interval": interval,
            "indicator": "OBV",
            "current_obv": float(df['obv'].iloc[-1]),
            "obv_trend": obv_trend,
            "price_trend": price_trend,
            "divergence": divergence,
            "interpretation": {
                "signal": "BULLISH" if obv_trend == "ALCISTA" and divergence != "BEARISH_DIVERGENCE" else "BEARISH",
                "strength": "STRONG" if divergence == "NO_DIVERGENCE" else "WEAK",
                "warning": "DIVERGENCE_DETECTED" if divergence != "NO_DIVERGENCE" else "NONE"
            },
            "values": [{"obv": float(val)} for val in obv_values[-10:]]
        }
        
    except Exception as e:
        logger.error(f"Error calculating OBV: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate OBV: {str(e)}"
        )


def analyze_volume_confirmation(symbol: str, interval: str = "1D", n_bars: int = 50) -> Dict[str, Any]:
    """
    Analyze volume confirmation for price movements.
    
    This function determines if recent price movements are confirmed by volume,
    which is crucial for validating the strength of trends.
    
    Args:
        symbol (str): Trading symbol
        interval (str): Time interval
        n_bars (int): Number of bars to analyze
        
    Returns:
        Dict[str, Any]: Volume confirmation analysis
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "close": bar["close"],
                "volume": bar["volume"]
            })
        
        df = pd.DataFrame(df_data)
        
        # Calculate volume moving average
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['price_change'] = df['close'].pct_change()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Recent analysis (last 5 bars)
        recent_data = df.tail(5)
        
        # Volume confirmation metrics
        avg_volume_ratio = recent_data['volume_ratio'].mean()
        strong_moves = recent_data[abs(recent_data['price_change']) > 0.02]  # Moves > 2%
        
        volume_confirmation = "CONFIRMED" if avg_volume_ratio > 1.2 else \
                            "WEAK" if avg_volume_ratio > 0.8 else "NOT_CONFIRMED"
        
        # Latest bar analysis
        latest_volume_ratio = float(recent_data['volume_ratio'].iloc[-1])
        latest_price_change = float(recent_data['price_change'].iloc[-1]) * 100
        
        return {
            "symbol": symbol,
            "interval": interval,
            "analysis_type": "VOLUME_CONFIRMATION",
            "latest_volume_ratio": round(latest_volume_ratio, 2),
            "latest_price_change_percent": round(latest_price_change, 2),
            "avg_volume_ratio_5d": round(avg_volume_ratio, 2),
            "volume_confirmation": volume_confirmation,
            "interpretation": {
                "signal": "BULLISH" if volume_confirmation == "CONFIRMED" and latest_price_change > 0 else \
                         "BEARISH" if volume_confirmation == "CONFIRMED" and latest_price_change < 0 else "NEUTRAL",
                "strength": "HIGH" if latest_volume_ratio > 1.5 else "MODERATE" if latest_volume_ratio > 1.0 else "LOW",
                "reliability": "HIGH" if volume_confirmation == "CONFIRMED" else "LOW"
            },
            "volume_data": [
                {
                    "volume_ratio": float(row['volume_ratio']),
                    "price_change": float(row['price_change']) * 100
                } for _, row in recent_data.iterrows()
            ]
        }
        
    except Exception as e:
        logger.error(f"Error analyzing volume confirmation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze volume confirmation: {str(e)}"
        )


def calculate_money_flow_index(symbol: str, interval: str = "1D", n_bars: int = 100, period: int = 14) -> Dict[str, Any]:
    """
    Calculate Money Flow Index (MFI).
    
    MFI is a momentum indicator that incorporates both price and volume data.
    It's often called the "volume-weighted RSI".
    
    Args:
        symbol (str): Trading symbol
        interval (str): Time interval
        n_bars (int): Number of bars to analyze
        period (int): Period for MFI calculation
        
    Returns:
        Dict[str, Any]: MFI values and interpretation
    """
    try:
        # Get price data
        price_data = get_price_data(symbol, interval, n_bars)
        
        # Convert to DataFrame
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"],
                "volume": bar["volume"]
            })
        
        df = pd.DataFrame(df_data)
        
        # Calculate typical price and money flow
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['money_flow'] = df['typical_price'] * df['volume']
        df['tp_change'] = df['typical_price'].diff()
        
        # Positive and negative money flow
        df['positive_mf'] = np.where(df['tp_change'] > 0, df['money_flow'], 0)
        df['negative_mf'] = np.where(df['tp_change'] < 0, df['money_flow'], 0)
        
        # Calculate MFI
        df['positive_mf_sum'] = df['positive_mf'].rolling(window=period).sum()
        df['negative_mf_sum'] = df['negative_mf'].rolling(window=period).sum()
        df['money_ratio'] = df['positive_mf_sum'] / df['negative_mf_sum']
        df['mfi'] = 100 - (100 / (1 + df['money_ratio']))
        
        # Current MFI and interpretation
        current_mfi = float(df['mfi'].iloc[-1])
        
        # MFI interpretation
        if current_mfi > 80:
            signal = "OVERBOUGHT"
            strength = "STRONG"
        elif current_mfi > 70:
            signal = "OVERBOUGHT"
            strength = "MODERATE"
        elif current_mfi < 20:
            signal = "OVERSOLD"
            strength = "STRONG"
        elif current_mfi < 30:
            signal = "OVERSOLD"
            strength = "MODERATE"
        else:
            signal = "NEUTRAL"
            strength = "MODERATE"
        
        return {
            "symbol": symbol,
            "interval": interval,
            "indicator": "MFI",
            "period": period,
            "current_mfi": round(current_mfi, 2),
            "signal": signal,
            "interpretation": {
                "condition": signal,
                "strength": strength,
                "recommendation": "SELL" if signal == "OVERBOUGHT" else "BUY" if signal == "OVERSOLD" else "HOLD"
            },
            "values": [{"mfi": float(val)} for val in df['mfi'].tail(10).tolist() if not pd.isna(val)]
        }
        
    except Exception as e:
        logger.error(f"Error calculating MFI: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate MFI: {str(e)}"
        )
