"""
Data Validation Utilities for TradingIA Backend.

This module provides robust validation for financial data including
quality checks, anomaly detection, and data cleaning functions.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

def validate_price_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate price data for quality and consistency.
    
    This function checks for common data quality issues in financial data
    and provides a comprehensive validation report.
    
    Args:
        data (Dict[str, Any]): Price data from get_price_data function
        
    Returns:
        Dict[str, Any]: Validation report with quality metrics and issues
    """
    try:
        validation_report = {
            "is_valid": True,
            "quality_score": 100,
            "issues": [],
            "warnings": [],
            "data_metrics": {},
            "recommendations": []
        }
        
        if not data or "data" not in data or not data["data"]:
            validation_report["is_valid"] = False
            validation_report["quality_score"] = 0
            validation_report["issues"].append("No data available")
            return validation_report
        
        # Convert to DataFrame for analysis
        df_data = []
        for bar in data["data"]:
            df_data.append({
                "datetime": bar["datetime"],
                "open": bar["open"],
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"],
                "volume": bar["volume"]
            })
        
        df = pd.DataFrame(df_data)
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # Basic data metrics
        validation_report["data_metrics"] = {
            "total_bars": len(df),
            "date_range": {
                "start": df['datetime'].min().isoformat(),
                "end": df['datetime'].max().isoformat()
            },
            "price_range": {
                "min": float(df[['open', 'high', 'low', 'close']].min().min()),
                "max": float(df[['open', 'high', 'low', 'close']].max().max())
            },
            "volume_stats": {
                "min": int(df['volume'].min()),
                "max": int(df['volume'].max()),
                "avg": int(df['volume'].mean())
            }
        }
        
        # Check for missing values
        missing_checks = _check_missing_values(df, validation_report)
        
        # Check for price consistency
        price_checks = _check_price_consistency(df, validation_report)
        
        # Check for gaps and anomalies
        gap_checks = _check_gaps_and_anomalies(df, validation_report)
        
        # Check volume data
        volume_checks = _check_volume_data(df, validation_report)
        
        # Check for duplicate timestamps
        duplicate_checks = _check_duplicates(df, validation_report)
        
        # Calculate final quality score
        validation_report["quality_score"] = _calculate_quality_score(validation_report)
        
        # Generate recommendations
        validation_report["recommendations"] = _generate_recommendations(validation_report)
        
        return validation_report
        
    except Exception as e:
        logger.error(f"Error validating price data: {str(e)}")
        return {
            "is_valid": False,
            "quality_score": 0,
            "issues": [f"Validation error: {str(e)}"],
            "warnings": [],
            "data_metrics": {},
            "recommendations": ["Unable to validate data due to error"]
        }


def _check_missing_values(df: pd.DataFrame, report: Dict[str, Any]) -> None:
    """Check for missing values in the dataset."""
    missing_counts = df.isnull().sum()
    
    for column, count in missing_counts.items():
        if count > 0:
            percentage = (count / len(df)) * 100
            if percentage > 10:
                report["issues"].append(f"High missing values in {column}: {count} ({percentage:.1f}%)")
                report["quality_score"] -= 20
            elif percentage > 5:
                report["warnings"].append(f"Some missing values in {column}: {count} ({percentage:.1f}%)")
                report["quality_score"] -= 10
            else:
                report["warnings"].append(f"Few missing values in {column}: {count} ({percentage:.1f}%)")
                report["quality_score"] -= 5


def _check_price_consistency(df: pd.DataFrame, report: Dict[str, Any]) -> None:
    """Check for price consistency (high >= low, etc.)."""
    # Check if high >= low
    invalid_hl = df[df['high'] < df['low']]
    if len(invalid_hl) > 0:
        report["issues"].append(f"Invalid high/low relationships in {len(invalid_hl)} bars")
        report["quality_score"] -= 15
    
    # Check if high >= open, close and low <= open, close
    invalid_prices = df[
        (df['high'] < df['open']) | 
        (df['high'] < df['close']) | 
        (df['low'] > df['open']) | 
        (df['low'] > df['close'])
    ]
    if len(invalid_prices) > 0:
        report["issues"].append(f"Invalid OHLC relationships in {len(invalid_prices)} bars")
        report["quality_score"] -= 15
    
    # Check for zero or negative prices
    zero_negative = df[(df[['open', 'high', 'low', 'close']] <= 0).any(axis=1)]
    if len(zero_negative) > 0:
        report["issues"].append(f"Zero or negative prices in {len(zero_negative)} bars")
        report["quality_score"] -= 25


def _check_gaps_and_anomalies(df: pd.DataFrame, report: Dict[str, Any]) -> None:
    """Check for price gaps and anomalies."""
    if len(df) < 2:
        return
    
    # Calculate price changes
    df['price_change'] = df['close'].pct_change()
    
    # Detect large gaps (>10% price change)
    large_gaps = df[abs(df['price_change']) > 0.10]
    if len(large_gaps) > 0:
        max_gap = abs(df['price_change']).max() * 100
        report["warnings"].append(f"Large price gaps detected: {len(large_gaps)} gaps, max: {max_gap:.1f}%")
        if max_gap > 50:
            report["quality_score"] -= 10
        elif max_gap > 20:
            report["quality_score"] -= 5
    
    # Check for extreme outliers (>5 standard deviations)
    price_std = df['price_change'].std()
    price_mean = df['price_change'].mean()
    outliers = df[abs(df['price_change'] - price_mean) > 5 * price_std]
    if len(outliers) > 0:
        report["warnings"].append(f"Extreme price outliers detected: {len(outliers)} bars")
        report["quality_score"] -= 5
    
    # Check for consecutive identical prices (potential data freeze)
    df['price_unchanged'] = (df['close'] == df['close'].shift(1))
    consecutive_unchanged = 0
    max_consecutive = 0
    
    for unchanged in df['price_unchanged']:
        if unchanged:
            consecutive_unchanged += 1
            max_consecutive = max(max_consecutive, consecutive_unchanged)
        else:
            consecutive_unchanged = 0
    
    if max_consecutive > 5:
        report["warnings"].append(f"Potential data freeze: {max_consecutive} consecutive unchanged prices")
        report["quality_score"] -= 10


def _check_volume_data(df: pd.DataFrame, report: Dict[str, Any]) -> None:
    """Check volume data quality."""
    # Check for zero volume
    zero_volume = df[df['volume'] == 0]
    if len(zero_volume) > 0:
        percentage = (len(zero_volume) / len(df)) * 100
        if percentage > 20:
            report["issues"].append(f"High zero volume bars: {len(zero_volume)} ({percentage:.1f}%)")
            report["quality_score"] -= 15
        elif percentage > 5:
            report["warnings"].append(f"Some zero volume bars: {len(zero_volume)} ({percentage:.1f}%)")
            report["quality_score"] -= 5
    
    # Check for negative volume
    negative_volume = df[df['volume'] < 0]
    if len(negative_volume) > 0:
        report["issues"].append(f"Negative volume in {len(negative_volume)} bars")
        report["quality_score"] -= 20
    
    # Check for extreme volume spikes
    if len(df) > 10:
        volume_median = df['volume'].median()
        volume_q99 = df['volume'].quantile(0.99)
        
        if volume_q99 > volume_median * 100:  # Volume spike >100x median
            extreme_spikes = df[df['volume'] > volume_median * 50]
            report["warnings"].append(f"Extreme volume spikes detected: {len(extreme_spikes)} bars")
            report["quality_score"] -= 5


def _check_duplicates(df: pd.DataFrame, report: Dict[str, Any]) -> None:
    """Check for duplicate timestamps."""
    duplicates = df[df['datetime'].duplicated()]
    if len(duplicates) > 0:
        report["issues"].append(f"Duplicate timestamps: {len(duplicates)} duplicates")
        report["quality_score"] -= 15


def _calculate_quality_score(report: Dict[str, Any]) -> int:
    """Calculate final quality score based on issues and warnings."""
    score = report["quality_score"]
    
    # Additional penalties for critical issues
    critical_issues = [issue for issue in report["issues"] if any(keyword in issue.lower() 
                      for keyword in ["negative", "invalid", "duplicate", "missing"])]
    
    if len(critical_issues) > 3:
        score -= 20
    elif len(critical_issues) > 1:
        score -= 10
    
    return max(0, min(100, score))


def _generate_recommendations(report: Dict[str, Any]) -> List[str]:
    """Generate recommendations based on validation results."""
    recommendations = []
    
    if report["quality_score"] < 50:
        recommendations.append("Data quality is poor - consider using alternative data source")
    elif report["quality_score"] < 70:
        recommendations.append("Data quality is moderate - use with caution")
    
    # Specific recommendations based on issues
    issues_text = " ".join(report["issues"] + report["warnings"]).lower()
    
    if "missing" in issues_text:
        recommendations.append("Consider data interpolation or gap filling for missing values")
    
    if "gap" in issues_text or "spike" in issues_text:
        recommendations.append("Apply outlier detection and smoothing for extreme values")
    
    if "volume" in issues_text:
        recommendations.append("Volume data may be unreliable - focus on price-based indicators")
    
    if "duplicate" in issues_text:
        recommendations.append("Remove duplicate timestamps before analysis")
    
    if "invalid" in issues_text:
        recommendations.append("Clean invalid price relationships before proceeding")
    
    if not recommendations:
        if report["quality_score"] >= 90:
            recommendations.append("Data quality is excellent - suitable for all analysis types")
        elif report["quality_score"] >= 80:
            recommendations.append("Data quality is good - suitable for most analysis types")
        else:
            recommendations.append("Data quality is acceptable - monitor for any issues")
    
    return recommendations


def clean_price_data(data: Dict[str, Any], aggressive: bool = False) -> Dict[str, Any]:
    """
    Clean price data by fixing common issues.
    
    Args:
        data (Dict[str, Any]): Raw price data
        aggressive (bool): Whether to apply aggressive cleaning
        
    Returns:
        Dict[str, Any]: Cleaned price data
    """
    try:
        if not data or "data" not in data or not data["data"]:
            return data
        
        # Convert to DataFrame
        df_data = []
        for bar in data["data"]:
            df_data.append({
                "datetime": bar["datetime"],
                "open": bar["open"],
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"],
                "volume": bar["volume"]
            })
        
        df = pd.DataFrame(df_data)
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # Remove duplicates
        df = df.drop_duplicates(subset=['datetime']).reset_index(drop=True)
        
        # Fix invalid OHLC relationships
        df['high'] = df[['open', 'high', 'low', 'close']].max(axis=1)
        df['low'] = df[['open', 'high', 'low', 'close']].min(axis=1)
        
        # Handle zero/negative prices
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            df[col] = df[col].replace(0, np.nan)
            df[col] = df[col].clip(lower=0.01)  # Minimum price of 0.01
        
        # Forward fill missing prices
        df[price_columns] = df[price_columns].fillna(method='ffill')
        
        # Handle volume issues
        df['volume'] = df['volume'].clip(lower=0)  # No negative volume
        df['volume'] = df['volume'].fillna(0)  # Fill missing volume with 0
        
        if aggressive:
            # Remove extreme outliers
            for col in price_columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 3 * IQR
                upper_bound = Q3 + 3 * IQR
                df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
            
            # Smooth extreme volume spikes
            volume_median = df['volume'].median()
            df['volume'] = df['volume'].clip(upper=volume_median * 100)
        
        # Convert back to original format
        cleaned_data = data.copy()
        cleaned_data["data"] = []
        
        for _, row in df.iterrows():
            cleaned_data["data"].append({
                "datetime": row['datetime'].isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": int(row['volume'])
            })
        
        cleaned_data["bars_count"] = len(df)
        if cleaned_data["data"]:
            cleaned_data["latest_price"] = cleaned_data["data"][-1]["close"]
            cleaned_data["latest_datetime"] = cleaned_data["data"][-1]["datetime"]
        
        return cleaned_data
        
    except Exception as e:
        logger.error(f"Error cleaning price data: {str(e)}")
        return data  # Return original data if cleaning fails


def detect_market_anomalies(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Detect market anomalies that might affect analysis.
    
    Args:
        data (Dict[str, Any]): Price data
        
    Returns:
        Dict[str, Any]: Anomaly detection report
    """
    try:
        if not data or "data" not in data or not data["data"]:
            return {"anomalies_detected": 0, "anomalies": []}
        
        df_data = []
        for bar in data["data"]:
            df_data.append({
                "datetime": bar["datetime"],
                "open": bar["open"],
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"],
                "volume": bar["volume"]
            })
        
        df = pd.DataFrame(df_data)
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        anomalies = []
        
        # Calculate returns
        df['returns'] = df['close'].pct_change()
        
        # Detect flash crashes/spikes (>15% single-bar moves)
        extreme_moves = df[abs(df['returns']) > 0.15]
        for _, row in extreme_moves.iterrows():
            anomalies.append({
                "type": "EXTREME_MOVE",
                "datetime": row['datetime'].isoformat(),
                "magnitude": f"{row['returns']*100:.1f}%",
                "severity": "HIGH"
            })
        
        # Detect volume anomalies
        if len(df) > 10:
            volume_mean = df['volume'].mean()
            volume_std = df['volume'].std()
            volume_anomalies = df[df['volume'] > volume_mean + 5 * volume_std]
            
            for _, row in volume_anomalies.iterrows():
                anomalies.append({
                    "type": "VOLUME_SPIKE",
                    "datetime": row['datetime'].isoformat(),
                    "volume": int(row['volume']),
                    "severity": "MEDIUM"
                })
        
        # Detect gaps (>5% overnight gaps)
        df['gap'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)
        large_gaps = df[abs(df['gap']) > 0.05]
        
        for _, row in large_gaps.iterrows():
            anomalies.append({
                "type": "PRICE_GAP",
                "datetime": row['datetime'].isoformat(),
                "gap_size": f"{row['gap']*100:.1f}%",
                "severity": "MEDIUM"
            })
        
        return {
            "anomalies_detected": len(anomalies),
            "anomalies": anomalies[-10:],  # Last 10 anomalies
            "summary": {
                "extreme_moves": len([a for a in anomalies if a["type"] == "EXTREME_MOVE"]),
                "volume_spikes": len([a for a in anomalies if a["type"] == "VOLUME_SPIKE"]),
                "price_gaps": len([a for a in anomalies if a["type"] == "PRICE_GAP"])
            }
        }
        
    except Exception as e:
        logger.error(f"Error detecting anomalies: {str(e)}")
        return {"anomalies_detected": 0, "anomalies": [], "error": str(e)}
