"""
Utilidades para manejo de horarios de mercado y zonas horarias
"""

import pytz
from datetime import datetime, time, timedelta
from typing import Tuple, List, Optional
import logging

logger = logging.getLogger(__name__)

# Configuración de mercados
MARKET_CONFIGS = {
    "US": {
        "timezone": "US/Eastern",
        "open_time": time(9, 30),  # 9:30 AM
        "close_time": time(16, 0),  # 4:00 PM
        "trading_days": [0, 1, 2, 3, 4],  # Lunes-Viernes
        "name": "US Stock Market"
    },
    "CRYPTO": {
        "timezone": "UTC",
        "open_time": time(0, 0),  # 24/7
        "close_time": time(23, 59),
        "trading_days": [0, 1, 2, 3, 4, 5, 6],  # Todos los días
        "name": "Cryptocurrency Market"
    },
    "FOREX": {
        "timezone": "UTC",
        "open_time": time(0, 0),  # 24/5
        "close_time": time(23, 59),
        "trading_days": [0, 1, 2, 3, 4],  # Lunes-Viernes
        "name": "Forex Market"
    }
}

# Días festivos US (simplificado - en producción usar biblioteca holidays)
US_HOLIDAYS_2025 = [
    "2025-01-01",  # New Year's Day
    "2025-01-20",  # Martin Luther King Jr. Day
    "2025-02-17",  # Presidents' Day
    "2025-04-18",  # Good Friday
    "2025-05-26",  # Memorial Day
    "2025-07-04",  # Independence Day
    "2025-09-01",  # Labor Day
    "2025-11-27",  # Thanksgiving
    "2025-12-25",  # Christmas Day
]

def get_market_timezone(market: str = "US") -> pytz.BaseTzInfo:
    """
    Obtiene la zona horaria de un mercado específico
    
    Args:
        market: Código del mercado (US, CRYPTO, FOREX)
        
    Returns:
        Objeto timezone de pytz
    """
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    return pytz.timezone(config["timezone"])

def is_market_day(date: Optional[datetime] = None, market: str = "US") -> bool:
    """
    Verifica si una fecha es día de trading para un mercado específico
    
    Args:
        date: Fecha a verificar (default: hoy)
        market: Código del mercado
        
    Returns:
        True si es día de trading
    """
    if date is None:
        date = datetime.now()
    
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    
    # Verificar día de la semana
    if date.weekday() not in config["trading_days"]:
        return False
    
    # Verificar días festivos (solo para US)
    if market == "US":
        date_str = date.strftime("%Y-%m-%d")
        if date_str in US_HOLIDAYS_2025:
            return False
    
    return True

def is_market_open(dt: Optional[datetime] = None, market: str = "US") -> bool:
    """
    Verifica si el mercado está abierto en un momento específico
    
    Args:
        dt: Datetime a verificar (default: ahora)
        market: Código del mercado
        
    Returns:
        True si el mercado está abierto
    """
    if dt is None:
        dt = datetime.now()
    
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    tz = get_market_timezone(market)
    
    # Convertir a zona horaria del mercado
    if dt.tzinfo is None:
        dt = tz.localize(dt)
    else:
        dt = dt.astimezone(tz)
    
    # Verificar si es día de trading
    if not is_market_day(dt, market):
        return False
    
    # Verificar horario
    current_time = dt.time()
    return config["open_time"] <= current_time <= config["close_time"]

def get_next_market_open(market: str = "US") -> datetime:
    """
    Obtiene la próxima apertura del mercado
    
    Args:
        market: Código del mercado
        
    Returns:
        Datetime de la próxima apertura
    """
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    tz = get_market_timezone(market)
    now = datetime.now(tz)
    
    # Si el mercado está abierto ahora, devolver la próxima apertura
    if is_market_open(now, market):
        # Buscar el próximo día de trading
        next_day = now + timedelta(days=1)
        while not is_market_day(next_day, market):
            next_day += timedelta(days=1)
        
        return next_day.replace(
            hour=config["open_time"].hour,
            minute=config["open_time"].minute,
            second=0,
            microsecond=0
        )
    
    # Si el mercado está cerrado, verificar si abre hoy
    today_open = now.replace(
        hour=config["open_time"].hour,
        minute=config["open_time"].minute,
        second=0,
        microsecond=0
    )
    
    if is_market_day(now, market) and now < today_open:
        return today_open
    
    # Buscar el próximo día de trading
    next_day = now + timedelta(days=1)
    while not is_market_day(next_day, market):
        next_day += timedelta(days=1)
    
    return next_day.replace(
        hour=config["open_time"].hour,
        minute=config["open_time"].minute,
        second=0,
        microsecond=0
    )

def get_market_close_time(market: str = "US") -> datetime:
    """
    Obtiene la hora de cierre del mercado para hoy
    
    Args:
        market: Código del mercado
        
    Returns:
        Datetime del cierre de hoy (o None si no es día de trading)
    """
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    tz = get_market_timezone(market)
    now = datetime.now(tz)
    
    if not is_market_day(now, market):
        return None
    
    return now.replace(
        hour=config["close_time"].hour,
        minute=config["close_time"].minute,
        second=0,
        microsecond=0
    )

def get_trading_schedule(market: str = "US", days: int = 7) -> List[Tuple[datetime, datetime]]:
    """
    Obtiene el horario de trading para los próximos N días
    
    Args:
        market: Código del mercado
        days: Número de días a incluir
        
    Returns:
        Lista de tuplas (apertura, cierre) para cada día de trading
    """
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    tz = get_market_timezone(market)
    now = datetime.now(tz)
    
    schedule = []
    current_date = now.date()
    
    for _ in range(days * 2):  # Buscar en más días para encontrar días de trading
        check_date = datetime.combine(current_date, time(0, 0))
        check_date = tz.localize(check_date)
        
        if is_market_day(check_date, market):
            open_time = check_date.replace(
                hour=config["open_time"].hour,
                minute=config["open_time"].minute
            )
            close_time = check_date.replace(
                hour=config["close_time"].hour,
                minute=config["close_time"].minute
            )
            schedule.append((open_time, close_time))
            
            if len(schedule) >= days:
                break
        
        current_date += timedelta(days=1)
    
    return schedule

def should_update_market_data(market: str = "US", force: bool = False) -> bool:
    """
    Determina si se deben actualizar los datos de mercado ahora
    
    Args:
        market: Código del mercado
        force: Forzar actualización independientemente del horario
        
    Returns:
        True si se debe actualizar
    """
    if force:
        return True
    
    # Para crypto, siempre actualizar
    if market == "CRYPTO":
        return True
    
    # Para otros mercados, solo durante días/horas de trading
    return is_market_open(market=market)

def format_market_status(market: str = "US") -> str:
    """
    Formatea el estado actual del mercado como string
    
    Args:
        market: Código del mercado
        
    Returns:
        String descriptivo del estado
    """
    config = MARKET_CONFIGS.get(market, MARKET_CONFIGS["US"])
    tz = get_market_timezone(market)
    now = datetime.now(tz)
    
    if is_market_open(now, market):
        close_time = get_market_close_time(market)
        if close_time:
            time_until_close = close_time - now
            hours, remainder = divmod(time_until_close.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            return f"{config['name']}: ABIERTO (cierra en {hours}h {minutes}m)"
    
    next_open = get_next_market_open(market)
    time_until_open = next_open - now
    
    if time_until_open.days > 0:
        return f"{config['name']}: CERRADO (abre en {time_until_open.days} días)"
    else:
        hours, remainder = divmod(time_until_open.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        return f"{config['name']}: CERRADO (abre en {hours}h {minutes}m)"
