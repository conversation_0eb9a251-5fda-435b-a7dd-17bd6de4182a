#!/usr/bin/env python3
"""
Script para poblar la base de datos con datos de mercado iniciales
"""

import sys
import asyncio
sys.path.append('app')

async def populate_initial_data():
    """Poblar base de datos con datos iniciales"""
    try:
        from app.services.market_data_service import market_data_service
        
        print('🚀 === INICIANDO POBLACIÓN DE DATOS DE MERCADO ===')
        
        # Actualizar todos los datos
        results = await market_data_service.update_market_data()
        
        print(f'✅ Datos actualizados:')
        print(f'   📊 Activos: {results["updated_assets"]}')
        print(f'   📈 Indicadores: {results["updated_indicators"]}')
        
        if results["errors"]:
            print(f'⚠️ Errores: {len(results["errors"])}')
            for error in results["errors"][:5]:  # Mostrar solo los primeros 5
                print(f'   ❌ {error}')
        
        # Obtener contexto para verificar
        print('\n🔍 === VERIFICANDO CONTEXTO GENERADO ===')
        context = await market_data_service.get_market_context_for_ai()
        print(f'Contexto generado ({len(context)} caracteres):')
        print(context[:500] + '...' if len(context) > 500 else context)
        
        return True
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(populate_initial_data())
    print(f'Resultado: {"ÉXITO" if result else "FALLO"}')
