[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tradingIA-backend"
version = "1.0.0"
description = "Backend API for TradingIA Financial AI Assistant"
readme = "../README.md"
license = {text = "MIT"}
authors = [
    {name = "TradingIA Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "TradingIA Team", email = "<EMAIL>"}
]
keywords = ["trading", "finance", "ai", "api", "fastapi", "technical-analysis"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "python-multipart==0.0.6",
    "pydantic==2.5.0",
    "pydantic-settings==2.1.0",
    "supabase==2.3.0",
    "google-cloud-aiplatform==1.38.1",
    "tvDatafeed==2.1.4",
    "stripe==7.8.0",
    "pandas-ta==0.3.14b0",
    "pandas==2.1.4",
    "numpy==1.25.2",
    "httpx==0.25.2",
    "python-dotenv==1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "flake8>=6.1.0",
    "isort>=5.12.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
]

[project.scripts]
tradingIA-server = "app.main:main"

[project.urls]
Homepage = "https://tradingIA.com"
Documentation = "https://docs.tradingIA.com"
Repository = "https://github.com/tradingIA/backend"
"Bug Tracker" = "https://github.com/tradingIA/backend/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]
exclude = ["tests*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
known_third_party = ["fastapi", "pydantic", "supabase", "vertexai"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "venv",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=80",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "auth: Tests requiring authentication",
    "external: Tests that call external APIs",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "venv"]
skips = ["B101", "B601"]
