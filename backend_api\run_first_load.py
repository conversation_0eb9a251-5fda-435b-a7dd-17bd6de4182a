#!/usr/bin/env python3
"""
Script para ejecutar la primera carga de análisis avanzados.
"""

import asyncio
import sys
import os
from datetime import datetime

# Añadir el directorio actual al path
sys.path.append('.')

async def main():
    """Ejecuta la primera carga de análisis avanzados."""
    try:
        print("🚀 Iniciando primera carga de análisis avanzados...")
        print(f"Fecha y hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        from app.services.market_data_service import market_data_service
        
        # Ejecutar análisis avanzados con configuración robusta
        print("📊 Ejecutando análisis avanzados...")
        print("⚠️ Nota: Algunos errores son esperados debido a problemas de conectividad con TradingView")
        print("🔄 Analizando TODOS los activos populares (incluyendo criptomonedas)...")
        print("💰 Ahora incluye: BTC, ETH, BNB, ADA, SOL, XRP y más cryptos!")

        results = await market_data_service.update_advanced_analysis(batch_size=3)
        
        print("\n✅ Primera carga completada:")
        print("=" * 40)
        print(f"   - Análisis de volumen: {results['updated_volume_analysis']}")
        print(f"   - Patrones detectados: {results['updated_patterns']}")
        print(f"   - Análisis multi-timeframe: {results['updated_multi_timeframe']}")
        print(f"   - Soporte/resistencia: {results['updated_support_resistance']}")
        print(f"   - Estructura de mercado: {results['updated_market_structure']}")
        print(f"   - Total activos procesados: {results['total_assets']}")
        
        if results['errors']:
            print(f"\n⚠️ Errores encontrados: {len(results['errors'])}")

            # Categorizar errores
            connection_errors = [e for e in results['errors'] if 'Connection' in e or 'no data' in e]
            db_errors = [e for e in results['errors'] if 'constraint' in e or 'database' in e]
            other_errors = [e for e in results['errors'] if e not in connection_errors and e not in db_errors]

            if connection_errors:
                print(f"   📡 Errores de conexión TradingView: {len(connection_errors)}")
                for i, error in enumerate(connection_errors[:3], 1):
                    print(f"      {i}. {error}")

            if db_errors:
                print(f"   🗄️ Errores de base de datos: {len(db_errors)}")
                for i, error in enumerate(db_errors[:3], 1):
                    print(f"      {i}. {error}")

            if other_errors:
                print(f"   ❓ Otros errores: {len(other_errors)}")
                for i, error in enumerate(other_errors[:3], 1):
                    print(f"      {i}. {error}")
        else:
            print("\n🎉 ¡Sin errores! Todos los análisis se completaron exitosamente.")
        
        # Mostrar estadísticas finales
        total_successful = (
            results['updated_volume_analysis'] + 
            results['updated_patterns'] + 
            results['updated_multi_timeframe'] + 
            results['updated_support_resistance'] + 
            results['updated_market_structure']
        )
        
        print(f"\n📈 Estadísticas finales:")
        print(f"   - Total análisis exitosos: {total_successful}")
        print(f"   - Total errores: {len(results['errors'])}")
        print(f"   - Tasa de éxito: {(total_successful / (total_successful + len(results['errors'])) * 100):.1f}%" if total_successful + len(results['errors']) > 0 else "N/A")
        
        return True
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        print("Asegúrate de que todas las dependencias estén instaladas.")
        return False
        
    except Exception as e:
        print(f"❌ Error en primera carga: {e}")
        import traceback
        print("\nDetalles del error:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 TradingIA - Primera Carga de Análisis Avanzados")
    print("=" * 60)
    
    success = asyncio.run(main())
    
    if success:
        print("\n🎯 Primera carga completada exitosamente!")
    else:
        print("\n💥 Primera carga falló. Revisa los errores arriba.")
    
    sys.exit(0 if success else 1)
