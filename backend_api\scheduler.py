#!/usr/bin/env python3
"""
Scheduler para actualización automática de datos de mercado
"""

import asyncio
import schedule
import time
import logging
from datetime import datetime
import sys
import os
from logging.handlers import RotatingFileHandler
import pytz

# Añadir el directorio app al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.market_data_service import market_data_service

# Configurar logging con rotación
def setup_logging():
    """Configura logging con archivos rotativos"""
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # Handler para archivo con rotación
    file_handler = RotatingFileHandler(
        'market_data_scheduler.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)

    # Handler para consola
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Formato
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Configurar logging
setup_logging()

logger = logging.getLogger(__name__)

async def update_market_data():
    """Actualiza los datos de mercado"""
    try:
        logger.info("🔄 Iniciando actualización programada de datos de mercado...")
        
        result = await market_data_service.update_market_data()
        
        logger.info(f"✅ Actualización completada: {result}")
        
        # Log estadísticas
        logger.info(f"📊 Activos actualizados: {result['updated_assets']}")
        logger.info(f"📈 Indicadores calculados: {result['updated_indicators']}")
        
        if result['errors']:
            logger.warning(f"⚠️ Errores encontrados: {len(result['errors'])}")
            for error in result['errors'][:5]:  # Solo los primeros 5
                logger.warning(f"   - {error}")
                
    except Exception as e:
        logger.error(f"❌ Error en actualización programada: {e}")

def run_update():
    """Wrapper síncrono para la actualización asíncrona"""
    asyncio.run(update_market_data())

def setup_scheduler():
    """Configura el programador de tareas con horarios específicos de mercado"""

    # Configuración de horarios específicos
    market_open_hour = 9
    market_close_hour = 16

    # Actualización cada 15 minutos durante horas de mercado (9:00-16:00)
    for hour in range(market_open_hour, market_close_hour + 1):
        for minute in [0, 15, 30, 45]:
            time_str = f"{hour:02d}:{minute:02d}"
            schedule.every().monday.at(time_str).do(run_update)
            schedule.every().tuesday.at(time_str).do(run_update)
            schedule.every().wednesday.at(time_str).do(run_update)
            schedule.every().thursday.at(time_str).do(run_update)
            schedule.every().friday.at(time_str).do(run_update)

    # Actualización especial al inicio del día de trading (9:00 AM)
    schedule.every().monday.at("09:00").do(run_update)
    schedule.every().tuesday.at("09:00").do(run_update)
    schedule.every().wednesday.at("09:00").do(run_update)
    schedule.every().thursday.at("09:00").do(run_update)
    schedule.every().friday.at("09:00").do(run_update)

    # Actualización al cierre del mercado (4:00 PM)
    schedule.every().monday.at("16:00").do(run_update)
    schedule.every().tuesday.at("16:00").do(run_update)
    schedule.every().wednesday.at("16:00").do(run_update)
    schedule.every().thursday.at("16:00").do(run_update)
    schedule.every().friday.at("16:00").do(run_update)

    # Actualización nocturna para criptomonedas (12:00 AM) - todos los días
    schedule.every().day.at("00:00").do(run_update)

    logger.info("📅 Scheduler configurado con horarios específicos:")
    logger.info("   - Cada 15 minutos durante horas de mercado (9:00-16:00)")
    logger.info("   - Solo días laborables (Lunes-Viernes)")
    logger.info("   - 09:00 AM (apertura mercado)")
    logger.info("   - 16:00 PM (cierre mercado)")
    logger.info("   - 00:00 AM (actualización cripto diaria)")

def scheduler_healthcheck():
    """Verifica el estado del scheduler"""
    try:
        pending_jobs = len(schedule.jobs)
        next_run = schedule.next_run()

        logger.info(f"💓 Healthcheck: {pending_jobs} trabajos programados")
        if next_run:
            logger.info(f"⏰ Próxima ejecución: {next_run}")

        return True
    except Exception as e:
        logger.error(f"❌ Error en healthcheck: {e}")
        return False

def is_market_day():
    """Verifica si hoy es día de mercado (Lunes-Viernes)"""
    est = pytz.timezone('US/Eastern')
    now_est = datetime.now(est)
    return now_est.weekday() < 5  # 0-4 = Lunes-Viernes

def main():
    """Función principal del scheduler"""
    logger.info("🚀 Iniciando Market Data Scheduler...")

    # Configurar tareas programadas
    setup_scheduler()

    # Ejecutar una actualización inicial solo si es día de mercado
    if is_market_day():
        logger.info("🔄 Ejecutando actualización inicial (día de mercado)...")
        run_update()
    else:
        logger.info("📅 Fin de semana detectado, solo actualizaciones cripto programadas")

    # Mantener el scheduler corriendo
    logger.info("⏰ Scheduler activo. Presiona Ctrl+C para detener.")

    try:
        while True:
            schedule.run_pending()

            # Healthcheck cada hora
            if datetime.now().minute == 0:
                scheduler_healthcheck()

            time.sleep(60)  # Verificar cada minuto
    except KeyboardInterrupt:
        logger.info("🛑 Scheduler detenido por el usuario")
    except Exception as e:
        logger.error(f"❌ Error en scheduler: {e}")
        # Reintentar después de 5 minutos en caso de error
        logger.info("🔄 Reintentando en 5 minutos...")
        time.sleep(300)

if __name__ == "__main__":
    main()
