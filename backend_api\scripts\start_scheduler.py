#!/usr/bin/env python3
"""
Script para iniciar el scheduler de datos de mercado como servicio
"""

import sys
import os
import logging
import signal
import time
from pathlib import Path

# Añadir el directorio raíz al path
root_dir = Path(__file__).parent.parent
sys.path.insert(0, str(root_dir))

from app.services.scheduler_service import scheduler_service
from app.config import settings

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler_service.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SchedulerServiceManager:
    """Gestor del servicio de scheduler"""
    
    def __init__(self):
        self.running = False
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Configura manejadores de señales para parada graceful"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # En Windows, SIGBREAK en lugar de SIGTERM
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Maneja señales de parada"""
        logger.info(f"📡 Señal recibida: {signum}")
        self.stop()
    
    def start(self):
        """Inicia el servicio de scheduler"""
        try:
            logger.info("🚀 Iniciando Scheduler Service Manager...")
            
            # Verificar configuración
            if not settings.scheduler_enabled:
                logger.warning("⚠️ Scheduler deshabilitado en configuración")
                return False
            
            # Iniciar scheduler
            result = scheduler_service.start()
            
            if result["status"] == "started":
                logger.info("✅ Scheduler iniciado correctamente")
                self.running = True
                return True
            else:
                logger.error(f"❌ Error iniciando scheduler: {result}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error crítico iniciando scheduler: {e}")
            return False
    
    def stop(self):
        """Detiene el servicio de scheduler"""
        try:
            logger.info("🛑 Deteniendo Scheduler Service...")
            
            result = scheduler_service.stop()
            
            if result["status"] in ["stopped", "already_stopped"]:
                logger.info("✅ Scheduler detenido correctamente")
            else:
                logger.warning(f"⚠️ Resultado de parada: {result}")
            
            self.running = False
            
        except Exception as e:
            logger.error(f"❌ Error deteniendo scheduler: {e}")
    
    def monitor(self):
        """Monitorea el estado del scheduler"""
        logger.info("👁️ Iniciando monitoreo del scheduler...")
        
        while self.running:
            try:
                # Verificar estado cada 60 segundos
                time.sleep(60)
                
                if not self.running:
                    break
                
                status = scheduler_service.get_status()
                
                if status["status"] == "error":
                    logger.warning("⚠️ Scheduler en estado de error, reintentando...")
                    scheduler_service.start()
                elif status["status"] == "stopped":
                    logger.warning("⚠️ Scheduler detenido inesperadamente, reiniciando...")
                    scheduler_service.start()
                else:
                    logger.debug(f"💓 Scheduler estado: {status['status']}")
                
            except KeyboardInterrupt:
                logger.info("⌨️ Interrupción de teclado detectada")
                break
            except Exception as e:
                logger.error(f"❌ Error en monitoreo: {e}")
                time.sleep(30)  # Esperar antes de reintentar
    
    def run(self):
        """Ejecuta el servicio completo"""
        try:
            # Iniciar scheduler
            if not self.start():
                logger.error("❌ No se pudo iniciar el scheduler")
                return 1
            
            # Monitorear
            self.monitor()
            
            # Parada graceful
            self.stop()
            
            logger.info("👋 Scheduler Service Manager terminado")
            return 0
            
        except Exception as e:
            logger.error(f"❌ Error crítico en servicio: {e}")
            self.stop()
            return 1

def main():
    """Función principal"""
    logger.info("=" * 50)
    logger.info("🤖 TradingIA Scheduler Service")
    logger.info("=" * 50)
    
    # Verificar configuración
    logger.info(f"📋 Configuración:")
    logger.info(f"   - Habilitado: {settings.scheduler_enabled}")
    logger.info(f"   - Intervalo: {settings.scheduler_interval_minutes} minutos")
    logger.info(f"   - Horario mercado: {settings.market_open_hour}:00-{settings.market_close_hour}:00")
    logger.info(f"   - Zona horaria: {settings.timezone}")
    
    # Crear y ejecutar manager
    manager = SchedulerServiceManager()
    exit_code = manager.run()
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
