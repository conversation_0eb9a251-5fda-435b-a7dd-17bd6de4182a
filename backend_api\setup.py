"""
Setup configuration for TradingIA Backend.

This file enables the backend to be packaged and distributed
as a Python package for deployment purposes.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), '..', 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "TradingIA Backend - Financial AI Assistant API"

# Read requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    with open(requirements_path, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="tradingIA-backend",
    version="1.0.0",
    description="Backend API for TradingIA Financial AI Assistant",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="TradingIA Team",
    author_email="<EMAIL>",
    url="https://github.com/tradingIA/backend",
    packages=find_packages(),
    include_package_data=True,
    install_requires=read_requirements(),
    python_requires=">=3.11",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Framework :: FastAPI",
        "Topic :: Office/Business :: Financial",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    keywords="trading, finance, ai, api, fastapi, technical-analysis",
    entry_points={
        "console_scripts": [
            "tradingIA-server=app.main:main",
        ],
    },
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-mock>=3.12.0",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "flake8>=6.1.0",
            "isort>=5.12.0",
            "bandit>=1.7.5",
            "safety>=2.3.0",
        ],
        "test": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-mock>=3.12.0",
            "httpx>=0.25.2",
        ],
    },
    project_urls={
        "Bug Reports": "https://github.com/tradingIA/backend/issues",
        "Source": "https://github.com/tradingIA/backend",
        "Documentation": "https://docs.tradingIA.com",
    },
)
