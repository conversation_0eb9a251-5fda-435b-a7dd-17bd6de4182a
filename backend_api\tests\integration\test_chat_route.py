"""
Integration tests for chat routes.

This module contains integration tests for the chat endpoint,
testing the complete flow with mocked external services.
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from datetime import datetime

from app.main import app
from app.models.chat import ChatMessage


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock user data for authentication tests."""
    return {
        "id": "user_123456789",
        "email": "<EMAIL>",
        "email_confirmed_at": "2025-01-01T00:00:00Z",
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "user_metadata": {},
        "app_metadata": {}
    }


@pytest.fixture
def valid_chat_request():
    """Valid chat request payload."""
    return {
        "history": [
            {
                "role": "user",
                "content": "Hola",
                "timestamp": "2025-01-01T10:00:00Z"
            },
            {
                "role": "assistant", 
                "content": "¡Hola! ¿En qué puedo ayudarte?",
                "timestamp": "2025-01-01T10:00:05Z"
            }
        ],
        "message": "¿Cuál es el precio de Tesla?"
    }


class TestChatEndpointAuthentication:
    """Test authentication for chat endpoint."""
    
    def test_chat_unauthenticated(self, client):
        """Test that chat endpoint returns 401 without authentication."""
        response = client.post("/api/v1/chat/", json={"message": "test", "history": []})
        
        assert response.status_code == 403  # FastAPI returns 403 for missing auth
    
    def test_chat_invalid_token(self, client):
        """Test that chat endpoint returns 401 with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.post(
            "/api/v1/chat/", 
            json={"message": "test", "history": []},
            headers=headers
        )
        
        assert response.status_code == 401
    
    @patch('app.services.supabase_client.validate_user_token')
    def test_chat_valid_token_but_validation_fails(self, mock_validate, client):
        """Test chat endpoint when token validation fails."""
        from fastapi import HTTPException, status
        
        mock_validate.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
        
        headers = {"Authorization": "Bearer valid_but_expired_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "test", "history": []},
            headers=headers
        )
        
        assert response.status_code == 401


class TestChatEndpointFlow:
    """Test the complete chat flow with mocked services."""
    
    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.send_function_result_to_model', new_callable=AsyncMock)
    @patch('app.routes.chat.get_price_data')
    @patch('app.routes.chat.generate_chat_response', new_callable=AsyncMock)
    @patch('app.routes.chat.initialize_gemini_model')
    @patch('app.routes.chat.validate_user_token', new_callable=AsyncMock)
    def test_chat_with_tool_call_success(
        self, 
        mock_validate, 
        mock_init_model, 
        mock_generate_response,
        mock_get_price_data,
        mock_send_result,
        mock_save_history,
        client, 
        mock_user, 
        valid_chat_request
    ):
        """Test successful chat flow with tool call."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # Mock AI requesting a tool call
        mock_generate_response.return_value = {
            "type": "function_call",
            "function_name": "get_price_data",
            "function_args": {
                "symbol": "NASDAQ:TSLA",
                "interval": "1D",
                "n_bars": 1
            }
        }
        
        # Mock tool execution result
        mock_get_price_data.return_value = {
            "symbol": "NASDAQ:TSLA",
            "interval": "1D",
            "bars_count": 1,
            "latest_price": 248.50,
            "data": [{
                "datetime": "2025-01-01T00:00:00",
                "open": 245.0,
                "high": 250.0,
                "low": 244.0,
                "close": 248.50,
                "volume": 1000000
            }]
        }
        
        # Mock final AI response
        mock_send_result.return_value = "El precio actual de Tesla (TSLA) es $248.50. Esta información es solo para fines educativos y no constituye asesoramiento financiero."
        
        # Mock save history (async)
        mock_save_history.return_value = AsyncMock()
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json=valid_chat_request,
            headers=headers
        )
        
        # Assertions
        assert response.status_code == 200
        
        response_data = response.json()
        assert "reply" in response_data
        assert "timestamp" in response_data
        assert "conversation_id" in response_data
        assert "Tesla" in response_data["reply"]
        assert "$248.50" in response_data["reply"]
        assert "asesoramiento financiero" in response_data["reply"]
        
        # Verify mocks were called
        mock_validate.assert_called_once()
        mock_init_model.assert_called_once()
        mock_generate_response.assert_called_once()
        mock_get_price_data.assert_called_once_with(
            symbol="NASDAQ:TSLA",
            interval="1D", 
            n_bars=1
        )
        mock_send_result.assert_called_once()
    
    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.generate_chat_response', new_callable=AsyncMock)
    @patch('app.routes.chat.initialize_gemini_model')
    @patch('app.routes.chat.validate_user_token', new_callable=AsyncMock)
    def test_chat_direct_text_response(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_save_history,
        client,
        mock_user,
        valid_chat_request
    ):
        """Test chat flow with direct text response (no tool calls)."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # Mock direct text response
        mock_generate_response.return_value = {
            "type": "text",
            "content": "¡Hola! Soy tu asistente financiero. ¿En qué puedo ayudarte hoy? Esta información es solo para fines educativos y no constituye asesoramiento financiero."
        }
        
        mock_save_history.return_value = AsyncMock()
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "Hola", "history": []},
            headers=headers
        )
        
        # Assertions
        assert response.status_code == 200
        
        response_data = response.json()
        assert "reply" in response_data
        assert "asistente financiero" in response_data["reply"]
        assert "asesoramiento financiero" in response_data["reply"]
    
    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.get_price_data')
    @patch('app.routes.chat.generate_chat_response', new_callable=AsyncMock)
    @patch('app.routes.chat.initialize_gemini_model')
    @patch('app.routes.chat.validate_user_token', new_callable=AsyncMock)
    def test_chat_tool_call_error_handling(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_get_price_data,
        mock_save_history,
        client,
        mock_user,
        valid_chat_request
    ):
        """Test error handling when tool execution fails."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # Mock AI requesting a tool call
        mock_generate_response.return_value = {
            "type": "function_call",
            "function_name": "get_price_data",
            "function_args": {
                "symbol": "INVALID:SYMBOL",
                "interval": "1D",
                "n_bars": 1
            }
        }
        
        # Mock tool execution failure
        from fastapi import HTTPException
        mock_get_price_data.side_effect = HTTPException(
            status_code=404,
            detail="No data found for symbol"
        )
        
        mock_save_history.return_value = AsyncMock()
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json=valid_chat_request,
            headers=headers
        )
        
        # Should still return 200 but with error message
        assert response.status_code == 200
        
        response_data = response.json()
        assert "error" in response_data["reply"].lower()
        assert "asesoramiento financiero" in response_data["reply"]


class TestChatHealthEndpoint:
    """Test the chat health endpoint."""
    
    def test_chat_health(self, client):
        """Test that chat health endpoint works."""
        response = client.get("/api/v1/chat/health")

        assert response.status_code == 200

        response_data = response.json()
        assert response_data["status"] == "healthy"
        assert response_data["service"] == "chat"
        assert "timestamp" in response_data
