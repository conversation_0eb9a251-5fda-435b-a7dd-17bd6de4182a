"""
Tests for advanced trading tools.

This module contains tests for the new advanced trading tools including
volume analysis, pattern recognition, multi-timeframe analysis, and market structure.
"""

import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
from fastapi import HTTPException

# Import the tools to test
from app.tools.volume_analysis import calculate_vwap, calculate_obv, analyze_volume_confirmation
from app.tools.pattern_recognition import detect_chart_patterns
from app.tools.multi_timeframe import get_multi_timeframe_analysis
from app.tools.advanced_indicators import identify_support_resistance, calculate_trend_strength
from app.tools.market_structure import analyze_market_structure


class TestVolumeAnalysis:
    """Test volume analysis tools."""
    
    @patch('app.tools.volume_analysis.get_price_data')
    def test_calculate_vwap_success(self, mock_get_price_data):
        """Test successful VWAP calculation."""
        # Mock price data
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": [
                {"high": 100, "low": 95, "close": 98, "volume": 1000},
                {"high": 102, "low": 97, "close": 100, "volume": 1200},
                {"high": 105, "low": 99, "close": 103, "volume": 800},
                {"high": 104, "low": 101, "close": 102, "volume": 1500},
                {"high": 106, "low": 103, "close": 105, "volume": 900}
            ]
        }
        mock_get_price_data.return_value = mock_data
        
        result = calculate_vwap("NASDAQ:TSLA", "1D", 5)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["indicator"] == "VWAP"
        assert "current_vwap" in result
        assert "current_price" in result
        assert "interpretation" in result
        assert result["interpretation"]["price_position"] in ["ABOVE_VWAP", "BELOW_VWAP"]
    
    @patch('app.tools.volume_analysis.get_price_data')
    def test_calculate_obv_success(self, mock_get_price_data):
        """Test successful OBV calculation."""
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": [
                {"close": 100, "volume": 1000},
                {"close": 102, "volume": 1200},
                {"close": 101, "volume": 800},
                {"close": 103, "volume": 1500},
                {"close": 105, "volume": 900}
            ]
        }
        mock_get_price_data.return_value = mock_data
        
        result = calculate_obv("NASDAQ:TSLA", "1D", 5)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["indicator"] == "OBV"
        assert "current_obv" in result
        assert "obv_trend" in result
        assert "divergence" in result
        assert result["obv_trend"] in ["ALCISTA", "BAJISTA"]
    
    @patch('app.tools.volume_analysis.get_price_data')
    def test_analyze_volume_confirmation_success(self, mock_get_price_data):
        """Test successful volume confirmation analysis."""
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": [
                {"close": 100, "volume": 1000},
                {"close": 102, "volume": 1500},
                {"close": 104, "volume": 1800},
                {"close": 103, "volume": 900},
                {"close": 105, "volume": 2000}
            ]
        }
        mock_get_price_data.return_value = mock_data
        
        result = analyze_volume_confirmation("NASDAQ:TSLA", "1D", 5)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["analysis_type"] == "VOLUME_CONFIRMATION"
        assert "volume_confirmation" in result
        assert result["volume_confirmation"] in ["CONFIRMED", "WEAK", "NOT_CONFIRMED"]
        assert "interpretation" in result


class TestPatternRecognition:
    """Test pattern recognition tools."""
    
    @patch('app.tools.pattern_recognition.get_price_data')
    def test_detect_chart_patterns_success(self, mock_get_price_data):
        """Test successful chart pattern detection."""
        # Create mock data that could form patterns
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": []
        }
        
        # Generate sample OHLC data
        for i in range(50):
            price = 100 + (i % 10) - 5  # Creates some variation
            mock_data["data"].append({
                "high": price + 2,
                "low": price - 2,
                "close": price,
                "volume": 1000 + (i % 500)
            })
        
        mock_get_price_data.return_value = mock_data
        
        result = detect_chart_patterns("NASDAQ:TSLA", "1D", 50)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["analysis_type"] == "CHART_PATTERNS"
        assert "patterns_detected" in result
        assert "patterns" in result
        assert "interpretation" in result
        assert isinstance(result["patterns"], list)
    
    @patch('app.tools.pattern_recognition.get_price_data')
    def test_detect_chart_patterns_insufficient_data(self, mock_get_price_data):
        """Test pattern detection with insufficient data."""
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": [
                {"high": 100, "low": 95, "close": 98, "volume": 1000}
            ]
        }
        mock_get_price_data.return_value = mock_data
        
        result = detect_chart_patterns("NASDAQ:TSLA", "1D", 1)
        
        assert result["patterns_detected"] == 0
        assert len(result["patterns"]) == 0


class TestMultiTimeframeAnalysis:
    """Test multi-timeframe analysis tools."""
    
    @patch('app.tools.multi_timeframe.get_price_data')
    @patch('app.tools.multi_timeframe.apply_indicator')
    def test_get_multi_timeframe_analysis_success(self, mock_apply_indicator, mock_get_price_data):
        """Test successful multi-timeframe analysis."""
        # Mock price data
        mock_get_price_data.return_value = {
            "symbol": "NASDAQ:TSLA",
            "latest_price": 105,
            "data": [{"close": 100}, {"close": 102}, {"close": 105}]
        }
        
        # Mock indicator data
        mock_apply_indicator.return_value = {
            "values": [{"sma": 100}, {"sma": 101}, {"sma": 102}]
        }
        
        result = get_multi_timeframe_analysis("NASDAQ:TSLA", ["1D", "4h"], 50)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["analysis_type"] == "MULTI_TIMEFRAME"
        assert "individual_analysis" in result
        assert "trend_alignment" in result
        assert "overall_analysis" in result
        assert "recommendation" in result
    
    @patch('app.tools.multi_timeframe.get_price_data')
    def test_multi_timeframe_with_error(self, mock_get_price_data):
        """Test multi-timeframe analysis with data error."""
        mock_get_price_data.side_effect = Exception("Data error")
        
        result = get_multi_timeframe_analysis("INVALID:SYMBOL", ["1D"], 50)
        
        assert result["symbol"] == "INVALID:SYMBOL"
        # Should handle errors gracefully
        assert "individual_analysis" in result


class TestAdvancedIndicators:
    """Test advanced indicator tools."""
    
    @patch('app.tools.advanced_indicators.get_price_data')
    def test_identify_support_resistance_success(self, mock_get_price_data):
        """Test successful support/resistance identification."""
        # Create mock data with clear support/resistance levels
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": []
        }
        
        # Generate data with repeated levels
        for i in range(100):
            if i % 20 == 0:  # Create resistance at 110
                price = 110
            elif i % 15 == 0:  # Create support at 90
                price = 90
            else:
                price = 100 + (i % 10) - 5
            
            mock_data["data"].append({
                "high": price + 1,
                "low": price - 1,
                "close": price
            })
        
        mock_get_price_data.return_value = mock_data
        
        result = identify_support_resistance("NASDAQ:TSLA", "1D", 100)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["analysis_type"] == "SUPPORT_RESISTANCE"
        assert "current_price" in result
        assert "resistance_levels" in result
        assert "support_levels" in result
        assert "interpretation" in result
    
    @patch('app.tools.advanced_indicators.get_price_data')
    def test_calculate_trend_strength_success(self, mock_get_price_data):
        """Test successful trend strength calculation."""
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": []
        }
        
        # Generate trending data
        for i in range(100):
            price = 100 + i * 0.5  # Uptrending data
            mock_data["data"].append({
                "high": price + 1,
                "low": price - 1,
                "close": price
            })
        
        mock_get_price_data.return_value = mock_data
        
        result = calculate_trend_strength("NASDAQ:TSLA", "1D", 100)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["indicator"] == "TREND_STRENGTH"
        assert "trend_direction" in result
        assert "strength_rating" in result
        assert "trend_score" in result
        assert result["trend_direction"] in ["BULLISH", "BEARISH", "SIDEWAYS"]


class TestMarketStructure:
    """Test market structure analysis tools."""
    
    @patch('app.tools.market_structure.get_price_data')
    def test_analyze_market_structure_success(self, mock_get_price_data):
        """Test successful market structure analysis."""
        mock_data = {
            "symbol": "NASDAQ:TSLA",
            "data": []
        }
        
        # Generate data with clear market structure
        for i in range(50):
            price = 100 + (i % 20) - 10  # Creates swing highs and lows
            mock_data["data"].append({
                "high": price + 2,
                "low": price - 2,
                "close": price,
                "datetime": f"2024-01-{i+1:02d}T10:00:00Z"
            })
        
        mock_get_price_data.return_value = mock_data
        
        result = analyze_market_structure("NASDAQ:TSLA", "1D", 50)
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["analysis_type"] == "MARKET_STRUCTURE"
        assert "trend_structure" in result
        assert "market_phase" in result
        assert "swing_points" in result
        assert "key_levels" in result
        assert "interpretation" in result


class TestErrorHandling:
    """Test error handling in advanced tools."""
    
    @patch('app.tools.volume_analysis.get_price_data')
    def test_vwap_with_invalid_data(self, mock_get_price_data):
        """Test VWAP calculation with invalid data."""
        mock_get_price_data.side_effect = Exception("Data fetch error")
        
        with pytest.raises(HTTPException):
            calculate_vwap("INVALID:SYMBOL", "1D", 50)
    
    @patch('app.tools.pattern_recognition.get_price_data')
    def test_pattern_detection_with_empty_data(self, mock_get_price_data):
        """Test pattern detection with empty data."""
        mock_get_price_data.return_value = {"symbol": "TEST", "data": []}
        
        result = detect_chart_patterns("TEST", "1D", 50)
        assert result["patterns_detected"] == 0
    
    def test_invalid_function_execution(self):
        """Test execution of invalid function."""
        from app.tools.tradingview_provider import execute_tool_function
        
        with pytest.raises(ValueError):
            execute_tool_function("invalid_function", {})


# Integration tests
class TestToolIntegration:
    """Test integration between different tools."""
    
    @patch('app.tools.tradingview_provider.get_price_data')
    def test_tool_execution_dispatcher(self, mock_get_price_data):
        """Test the tool execution dispatcher."""
        from app.tools.tradingview_provider import execute_tool_function
        
        mock_get_price_data.return_value = {
            "symbol": "NASDAQ:TSLA",
            "data": [
                {"high": 100, "low": 95, "close": 98, "volume": 1000},
                {"high": 102, "low": 97, "close": 100, "volume": 1200}
            ]
        }
        
        # Test VWAP execution through dispatcher
        result = execute_tool_function("calculate_vwap", {
            "symbol": "NASDAQ:TSLA",
            "interval": "1D",
            "n_bars": 2
        })
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["indicator"] == "VWAP"
    
    def test_all_tools_available(self):
        """Test that all tools are available in the dispatcher."""
        from app.tools.tradingview_provider import execute_tool_function
        
        expected_functions = [
            "get_price_data",
            "apply_indicator", 
            "calculate_vwap",
            "calculate_obv",
            "analyze_volume_confirmation",
            "detect_chart_patterns",
            "get_multi_timeframe_analysis",
            "identify_support_resistance",
            "calculate_trend_strength",
            "analyze_market_structure"
        ]
        
        # This test verifies the function mapping exists
        # We can't test execution without mocking all dependencies
        for func_name in expected_functions:
            try:
                # This should raise ValueError for unknown functions
                # or other exceptions for known functions without proper args
                execute_tool_function(func_name, {})
            except ValueError as e:
                if "Unknown function" in str(e):
                    pytest.fail(f"Function {func_name} not found in dispatcher")
            except Exception:
                # Expected for functions that need proper arguments
                pass
