"""
Unit tests for service modules.

This module contains unit tests for the Supabase and Vertex AI
service modules with mocked dependencies.
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTTPException

from app.services.supabase_client import (
    create_supabase_client, 
    get_supabase_client,
    validate_user_token,
    save_chat_history
)
from app.services.vertex_ai import (
    initialize_gemini_model,
    generate_chat_response,
    send_function_result_to_model
)


class TestSupabaseClient:
    """Test cases for Supabase client functions."""
    
    @patch('app.services.supabase_client.create_client')
    def test_create_supabase_client_success(self, mock_create_client):
        """Test successful Supabase client creation."""
        mock_client = MagicMock()
        mock_create_client.return_value = mock_client
        
        result = create_supabase_client()
        
        assert result == mock_client
        mock_create_client.assert_called_once()
    
    @patch('app.services.supabase_client.create_client')
    def test_create_supabase_client_failure(self, mock_create_client):
        """Test Supabase client creation failure."""
        mock_create_client.side_effect = Exception("Connection failed")
        
        with pytest.raises(HTTPException) as exc_info:
            create_supabase_client()
        
        assert exc_info.value.status_code == 500
        assert "Database connection failed" in str(exc_info.value.detail)
    
    @patch('app.services.supabase_client.create_supabase_client')
    def test_get_supabase_client_singleton(self, mock_create):
        """Test that get_supabase_client implements singleton pattern."""
        mock_client = MagicMock()
        mock_create.return_value = mock_client
        
        # Reset the global client
        import app.services.supabase_client
        app.services.supabase_client._supabase_client = None
        
        # First call should create client
        client1 = get_supabase_client()
        assert client1 == mock_client
        mock_create.assert_called_once()
        
        # Second call should reuse existing client
        client2 = get_supabase_client()
        assert client2 == mock_client
        assert client1 is client2
        # create should still only be called once
        mock_create.assert_called_once()
    
    @patch('app.services.supabase_client.get_supabase_client')
    @pytest.mark.asyncio
    async def test_validate_user_token_success(self, mock_get_client, mock_user):
        """Test successful token validation."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        # Mock successful auth response
        mock_response = MagicMock()
        mock_response.user = MagicMock()
        mock_response.user.id = mock_user["id"]
        mock_response.user.email = mock_user["email"]
        mock_response.user.email_confirmed_at = mock_user["email_confirmed_at"]
        mock_response.user.created_at = mock_user["created_at"]
        mock_response.user.updated_at = mock_user["updated_at"]
        mock_response.user.user_metadata = mock_user["user_metadata"]
        mock_response.user.app_metadata = mock_user["app_metadata"]
        
        mock_client.auth.get_user.return_value = mock_response
        
        result = await validate_user_token("valid_token")
        
        assert result["id"] == mock_user["id"]
        assert result["email"] == mock_user["email"]
        mock_client.auth.get_user.assert_called_once_with("valid_token")
    
    @patch('app.services.supabase_client.get_supabase_client')
    @pytest.mark.asyncio
    async def test_validate_user_token_bearer_prefix(self, mock_get_client):
        """Test token validation with Bearer prefix."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        mock_response = MagicMock()
        mock_response.user = None
        mock_client.auth.get_user.return_value = mock_response
        
        with pytest.raises(HTTPException) as exc_info:
            await validate_user_token("Bearer invalid_token")
        
        assert exc_info.value.status_code == 401
        # Should call with token without Bearer prefix
        mock_client.auth.get_user.assert_called_once_with("invalid_token")
    
    @patch('app.services.supabase_client.get_supabase_client')
    @pytest.mark.asyncio
    async def test_validate_user_token_no_user(self, mock_get_client):
        """Test token validation when no user found."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        mock_response = MagicMock()
        mock_response.user = None
        mock_client.auth.get_user.return_value = mock_response
        
        with pytest.raises(HTTPException) as exc_info:
            await validate_user_token("invalid_token")
        
        assert exc_info.value.status_code == 401
        assert "Invalid authentication token" in str(exc_info.value.detail)
    
    @patch('app.services.supabase_client.get_supabase_client')
    @pytest.mark.asyncio
    async def test_save_chat_history_success(self, mock_get_client):
        """Test successful chat history saving."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        # Mock successful insert response
        mock_response = MagicMock()
        mock_response.data = [{
            "id": "chat_123",
            "created_at": "2025-01-01T10:00:00Z"
        }]
        mock_client.table.return_value.insert.return_value.execute.return_value = mock_response
        
        result = await save_chat_history(
            user_id="user_123",
            request_messages=[{"role": "user", "content": "test"}],
            ai_response="Test response",
            conversation_id="conv_123"
        )
        
        assert result["id"] == "chat_123"
        assert result["conversation_id"] == "conv_123"
        mock_client.table.assert_called_once_with("chat_history")
    
    @patch('app.services.supabase_client.get_supabase_client')
    @pytest.mark.asyncio
    async def test_save_chat_history_failure(self, mock_get_client):
        """Test chat history saving failure."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        # Mock failed insert response
        mock_response = MagicMock()
        mock_response.data = None
        mock_client.table.return_value.insert.return_value.execute.return_value = mock_response
        
        with pytest.raises(HTTPException) as exc_info:
            await save_chat_history(
                user_id="user_123",
                request_messages=[],
                ai_response="Test response"
            )
        
        assert exc_info.value.status_code == 500
        assert "Failed to save chat history" in str(exc_info.value.detail)


class TestVertexAI:
    """Test cases for Vertex AI service functions."""
    
    @patch('app.services.vertex_ai.GenerativeModel')
    @patch('app.services.vertex_ai.Tool')
    @patch('app.services.vertex_ai.FunctionDeclaration')
    @patch('app.services.vertex_ai.vertexai')
    def test_initialize_gemini_model_success(
        self, 
        mock_vertexai, 
        mock_function_declaration,
        mock_tool,
        mock_generative_model
    ):
        """Test successful Gemini model initialization."""
        mock_model = MagicMock()
        mock_generative_model.return_value = mock_model
        
        tools = [
            {
                "name": "test_tool",
                "description": "Test tool",
                "parameters": {"type": "object"}
            }
        ]
        
        result = initialize_gemini_model(tools)
        
        assert result == mock_model
        mock_vertexai.init.assert_called_once()
        mock_function_declaration.assert_called_once()
        mock_generative_model.assert_called_once()
    
    @patch('app.services.vertex_ai.vertexai')
    def test_initialize_gemini_model_failure(self, mock_vertexai):
        """Test Gemini model initialization failure."""
        mock_vertexai.init.side_effect = Exception("Vertex AI init failed")
        
        with pytest.raises(HTTPException) as exc_info:
            initialize_gemini_model([])
        
        assert exc_info.value.status_code == 500
        assert "AI model initialization failed" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_generate_chat_response_text(self):
        """Test chat response generation with text response."""
        mock_model = MagicMock()
        
        # Mock text response
        mock_part = MagicMock()
        mock_part.text = "This is a test response"
        mock_part.function_call = None
        
        mock_candidate = MagicMock()
        mock_candidate.content.parts = [mock_part]
        
        mock_response = MagicMock()
        mock_response.candidates = [mock_candidate]
        
        mock_model.generate_content.return_value = mock_response
        
        messages = [{"role": "user", "content": "Hello"}]
        result = await generate_chat_response(messages, mock_model)
        
        assert result["type"] == "text"
        assert result["content"] == "This is a test response"
    
    @pytest.mark.asyncio
    async def test_generate_chat_response_function_call(self):
        """Test chat response generation with function call."""
        mock_model = MagicMock()
        
        # Mock function call response
        mock_function_call = MagicMock()
        mock_function_call.name = "get_price_data"
        mock_function_call.args = {"symbol": "TSLA", "interval": "1D"}
        
        mock_part = MagicMock()
        mock_part.function_call = mock_function_call
        mock_part.text = None
        
        mock_candidate = MagicMock()
        mock_candidate.content.parts = [mock_part]
        
        mock_response = MagicMock()
        mock_response.candidates = [mock_candidate]
        
        mock_model.generate_content.return_value = mock_response
        
        messages = [{"role": "user", "content": "Get Tesla price"}]
        result = await generate_chat_response(messages, mock_model)
        
        assert result["type"] == "function_call"
        assert result["function_name"] == "get_price_data"
        assert result["function_args"]["symbol"] == "TSLA"
    
    @pytest.mark.asyncio
    async def test_send_function_result_to_model(self):
        """Test sending function result back to model."""
        mock_model = MagicMock()
        
        # Mock text response
        mock_part = MagicMock()
        mock_part.text = "Based on the data, Tesla is trading at $248.50"
        
        mock_candidate = MagicMock()
        mock_candidate.content.parts = [mock_part]
        
        mock_response = MagicMock()
        mock_response.candidates = [mock_candidate]
        
        mock_model.generate_content.return_value = mock_response
        
        result = await send_function_result_to_model(
            model=mock_model,
            conversation_history="User: Get Tesla price",
            function_name="get_price_data",
            function_result={"price": 248.50}
        )
        
        assert "Tesla is trading at $248.50" in result
        mock_model.generate_content.assert_called_once()
