#!/usr/bin/env node

/**
 * Code Dump Generator for TradingIA Project
 * 
 * This script generates a comprehensive text file containing all source code files
 * from the TradingIA project, organized by directory structure.
 * 
 * Usage: node create_code_dump.js
 * Output: code_dump_YYYYMMDD_HHMMSS.txt
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
    outputFileName: `code_dump_${new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)}.txt`,
    projectName: 'TradingIA - Asistente Financiero IA',
    includeExtensions: ['.py', '.js', '.ts', '.tsx', '.json', '.yml', '.yaml', '.md', '.txt', '.ini', '.toml', '.cfg'],
    excludeDirectories: ['node_modules', '__pycache__', '.git', 'venv', 'env', 'dist', 'build', '.pytest_cache', 'htmlcov'],
    excludeFiles: ['.DS_Store', 'Thumbs.db', '*.pyc', '*.pyo', '*.log'],
    maxFileSize: 1024 * 1024, // 1MB max file size
    encoding: 'utf8'
};

/**
 * Check if a file should be included based on extension
 */
function shouldIncludeFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath);
    
    // Check extension
    if (!CONFIG.includeExtensions.includes(ext)) {
        return false;
    }
    
    // Check excluded files
    if (CONFIG.excludeFiles.some(pattern => {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(fileName);
        }
        return fileName === pattern;
    })) {
        return false;
    }
    
    return true;
}

/**
 * Check if a directory should be excluded
 */
function shouldExcludeDirectory(dirPath) {
    const dirName = path.basename(dirPath);
    return CONFIG.excludeDirectories.includes(dirName);
}

/**
 * Get file size in bytes
 */
function getFileSize(filePath) {
    try {
        const stats = fs.statSync(filePath);
        return stats.size;
    } catch (error) {
        return 0;
    }
}

/**
 * Recursively scan directory for code files
 */
function scanDirectory(dirPath, basePath = '') {
    const files = [];
    
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const relativePath = path.join(basePath, item);
            
            try {
                const stats = fs.statSync(fullPath);
                
                if (stats.isDirectory()) {
                    if (!shouldExcludeDirectory(fullPath)) {
                        files.push(...scanDirectory(fullPath, relativePath));
                    }
                } else if (stats.isFile()) {
                    if (shouldIncludeFile(fullPath)) {
                        const fileSize = getFileSize(fullPath);
                        if (fileSize <= CONFIG.maxFileSize) {
                            files.push({
                                path: relativePath,
                                fullPath: fullPath,
                                size: fileSize,
                                extension: path.extname(fullPath)
                            });
                        } else {
                            console.warn(`Skipping large file: ${relativePath} (${Math.round(fileSize / 1024)}KB)`);
                        }
                    }
                }
            } catch (error) {
                console.warn(`Error processing ${fullPath}: ${error.message}`);
            }
        }
    } catch (error) {
        console.error(`Error reading directory ${dirPath}: ${error.message}`);
    }
    
    return files;
}

/**
 * Read file content safely
 */
function readFileContent(filePath) {
    try {
        return fs.readFileSync(filePath, CONFIG.encoding);
    } catch (error) {
        return `[ERROR READING FILE: ${error.message}]`;
    }
}

/**
 * Generate separator line
 */
function generateSeparator(char = '=', length = 80) {
    return char.repeat(length);
}

/**
 * Generate file header
 */
function generateFileHeader(fileInfo) {
    const header = [
        '',
        generateSeparator('='),
        `FILE: ${fileInfo.path}`,
        `SIZE: ${fileInfo.size} bytes`,
        `TYPE: ${fileInfo.extension}`,
        generateSeparator('='),
        ''
    ];
    return header.join('\n');
}

/**
 * Generate project header
 */
function generateProjectHeader() {
    const timestamp = new Date().toISOString();
    const header = [
        generateSeparator('='),
        `${CONFIG.projectName.toUpperCase()} - CODE DUMP`,
        generateSeparator('='),
        '',
        `Generated: ${timestamp}`,
        `Generator: create_code_dump.js`,
        '',
        'This file contains all source code files from the TradingIA project.',
        'Each file is clearly marked with its path and metadata.',
        '',
        generateSeparator('-'),
        'PROJECT STRUCTURE AND CONTENTS',
        generateSeparator('-'),
        ''
    ];
    return header.join('\n');
}

/**
 * Generate table of contents
 */
function generateTableOfContents(files) {
    const toc = [
        'TABLE OF CONTENTS',
        generateSeparator('-'),
        ''
    ];
    
    // Group files by directory
    const directories = {};
    files.forEach(file => {
        const dir = path.dirname(file.path) || '.';
        if (!directories[dir]) {
            directories[dir] = [];
        }
        directories[dir].push(file);
    });
    
    // Sort directories
    const sortedDirs = Object.keys(directories).sort();
    
    sortedDirs.forEach(dir => {
        toc.push(`📁 ${dir}/`);
        directories[dir]
            .sort((a, b) => a.path.localeCompare(b.path))
            .forEach(file => {
                const fileName = path.basename(file.path);
                const sizeKB = Math.round(file.size / 1024) || '<1';
                toc.push(`   📄 ${fileName} (${sizeKB}KB)`);
            });
        toc.push('');
    });
    
    toc.push(generateSeparator('-'));
    toc.push(`Total files: ${files.length}`);
    toc.push(`Total size: ${Math.round(files.reduce((sum, f) => sum + f.size, 0) / 1024)}KB`);
    toc.push('');
    
    return toc.join('\n');
}

/**
 * Main function
 */
function main() {
    console.log('🚀 Starting TradingIA Code Dump Generation...');
    
    const startTime = Date.now();
    const projectRoot = process.cwd();
    
    console.log(`📁 Scanning project directory: ${projectRoot}`);
    
    // Scan for files
    const files = scanDirectory(projectRoot);
    
    if (files.length === 0) {
        console.error('❌ No files found to include in dump');
        process.exit(1);
    }
    
    console.log(`📄 Found ${files.length} files to include`);
    
    // Sort files by path
    files.sort((a, b) => a.path.localeCompare(b.path));
    
    // Generate content
    let content = '';
    
    // Add project header
    content += generateProjectHeader();
    
    // Add table of contents
    content += generateTableOfContents(files);
    
    // Add file contents
    console.log('📝 Processing file contents...');
    
    files.forEach((file, index) => {
        console.log(`   Processing ${index + 1}/${files.length}: ${file.path}`);
        
        content += generateFileHeader(file);
        content += readFileContent(file.fullPath);
        content += '\n\n';
    });
    
    // Add footer
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    content += generateSeparator('=');
    content += '\nEND OF CODE DUMP\n';
    content += generateSeparator('=');
    content += `\nGenerated in ${duration} seconds\n`;
    content += `Total files processed: ${files.length}\n`;
    content += `Total content size: ${Math.round(content.length / 1024)}KB\n`;
    
    // Write output file
    const outputPath = path.join(projectRoot, CONFIG.outputFileName);
    
    try {
        fs.writeFileSync(outputPath, content, CONFIG.encoding);
        console.log(`✅ Code dump generated successfully!`);
        console.log(`📄 Output file: ${CONFIG.outputFileName}`);
        console.log(`📊 Files processed: ${files.length}`);
        console.log(`📏 Output size: ${Math.round(content.length / 1024)}KB`);
        console.log(`⏱️  Generation time: ${duration}s`);
    } catch (error) {
        console.error(`❌ Error writing output file: ${error.message}`);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    scanDirectory,
    shouldIncludeFile,
    shouldExcludeDirectory,
    generateProjectHeader,
    generateTableOfContents,
    CONFIG
};
