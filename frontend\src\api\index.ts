import { apiClient } from '@/lib/apiClient'
import type { ChatRequest, ChatResponse, ChatMessage, ConversationSummary } from '@/types/chat'

// Chat API endpoints
export const chatApi = {
  /**
   * Send a chat message to the AI assistant
   */
  sendMessage: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await apiClient.post<ChatResponse>('/api/v1/chat/', request)
    return response.data
  },

  /**
   * Get chat health status
   */
  getHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await apiClient.get('/api/v1/chat/health')
    return response.data
  },

  /**
   * Get market analysis
   */
  getMarketAnalysis: async (): Promise<{ analysis: string; timestamp: string; status: string }> => {
    // Use debug endpoint for development
    const endpoint = import.meta.env.DEV
      ? '/api/v1/chat/market-analysis/debug'
      : '/api/v1/chat/market-analysis'
    const response = await apiClient.get(endpoint)
    return response.data
  },

  /**
   * Get buy opportunities
   */
  getBuyOpportunities: async (): Promise<{ opportunities: string; timestamp: string; status: string }> => {
    // Use debug endpoint for development
    const endpoint = import.meta.env.DEV
      ? '/api/v1/chat/buy-opportunities/debug'
      : '/api/v1/chat/buy-opportunities'
    const response = await apiClient.get(endpoint)
    return response.data
  },

  /**
   * Start scheduler
   */
  startScheduler: async (): Promise<{ status: string; message: string; result: any }> => {
    const response = await apiClient.post('/api/v1/chat/scheduler/start')
    return response.data
  },

  /**
   * Stop scheduler
   */
  stopScheduler: async (): Promise<{ status: string; message: string; result: any }> => {
    const response = await apiClient.post('/api/v1/chat/scheduler/stop')
    return response.data
  },

  /**
   * Get scheduler status
   */
  getSchedulerStatus: async (): Promise<{ scheduler_status: any; timestamp: string; status: string }> => {
    const response = await apiClient.get('/api/v1/chat/scheduler/status')
    return response.data
  },

  /**
   * Trigger manual update
   */
  triggerManualUpdate: async (params?: {
    batch_size?: number;
    specific_symbols?: string
  }): Promise<{ result: any; message: string; status: string }> => {
    const endpoint = import.meta.env.DEV
      ? '/api/v1/chat/update-market-data/debug'
      : '/api/v1/chat/update-market-data'

    const queryParams = new URLSearchParams()
    if (params?.batch_size) queryParams.append('batch_size', params.batch_size.toString())
    if (params?.specific_symbols) queryParams.append('specific_symbols', params.specific_symbols)

    const url = queryParams.toString() ? `${endpoint}?${queryParams}` : endpoint
    const response = await apiClient.post(url)
    return response.data
  },

  /**
   * Trigger full data reload (complete market data refresh)
   */
  triggerFullDataReload: async (): Promise<{
    message: string;
    results: {
      cleaning_results: any;
      basic_data: any;
      advanced_analysis: any;
      total_time_seconds: number;
      start_time: string;
      end_time: string;
      status: string;
      summary: {
        total_assets_processed: number;
        total_indicators_calculated: number;
        total_volume_analysis: number;
        total_patterns_detected: number;
        total_multi_timeframe: number;
        total_support_resistance: number;
        total_market_structure: number;
        total_errors: number;
        success_rate: number;
        tables_cleaned: number;
        records_deleted: number;
      };
    };
    timestamp: string;
    status: string;
  }> => {
    const response = await apiClient.post('/api/v1/chat/full-data-reload')
    return response.data
  },
}

// Chat History API endpoints (will interact with Supabase directly in hooks)
export const chatHistoryApi = {
  /**
   * Get user's conversation summaries
   */
  getConversations: async (): Promise<ConversationSummary[]> => {
    // This will be implemented in the useChatHistory hook using Supabase client
    // Placeholder for now
    return []
  },

  /**
   * Get full conversation by ID
   */
  getConversation: async (_conversationId: string): Promise<ChatMessage[]> => {
    // This will be implemented in the useChatHistory hook using Supabase client
    // Placeholder for now
    return []
  },

  /**
   * Delete a conversation
   */
  deleteConversation: async (_conversationId: string): Promise<void> => {
    // This will be implemented in the useChatHistory hook using Supabase client
    // Placeholder for now
  },
}

// Health check for the entire backend
export const healthApi = {
  /**
   * Check backend health
   */
  checkHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await apiClient.get('/health')
    return response.data
  },
}

// Export all APIs
export const api = {
  chat: chatApi,
  chatHistory: chatHistoryApi,
  health: healthApi,
}

// Export types
export type { ChatRequest, ChatResponse }
