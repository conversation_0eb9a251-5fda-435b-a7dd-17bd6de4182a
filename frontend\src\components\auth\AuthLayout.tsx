import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { cn } from '@/utils/cn'

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  description?: string
  className?: string
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  description,
  className,
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo and branding */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 rounded-full bg-blue-600 flex items-center justify-center mb-4">
            <svg
              className="h-6 w-6 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            TradingIA
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Asistente Financiero IA
          </p>
        </div>

        {/* Auth card */}
        <Card className={cn('shadow-lg', className)}>
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-xl">{title}</CardTitle>
            {description && (
              <CardDescription>{description}</CardDescription>
            )}
          </CardHeader>
          <CardContent>{children}</CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500 dark:text-gray-400">
          <p>
            Al continuar, aceptas nuestros{' '}
            <a
              href="/terms"
              className="underline hover:text-gray-700 dark:hover:text-gray-300"
            >
              Términos de Servicio
            </a>{' '}
            y{' '}
            <a
              href="/privacy"
              className="underline hover:text-gray-700 dark:hover:text-gray-300"
            >
              Política de Privacidad
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
