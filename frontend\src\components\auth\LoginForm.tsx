import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { useAuth } from '@/hooks/useAuth'
import { loginSchema, type LoginFormData } from '@/utils/validators'
import { cn } from '@/utils/cn'

interface LoginFormProps {
  onToggleMode?: () => void
  onSuccess?: () => void
  className?: string
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onToggleMode,
  onSuccess,
  className,
}) => {
  const { signIn, isLoading } = useAuth()
  const [serverError, setServerError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    try {
      setServerError(null)
      
      const { user, error } = await signIn(data)
      
      if (error) {
        // Handle specific error types
        if (error.includes('Invalid login credentials')) {
          setError('email', { message: 'Email o contraseña incorrectos' })
          setError('password', { message: 'Email o contraseña incorrectos' })
        } else if (error.includes('Email not confirmed')) {
          setServerError('Por favor, confirma tu email antes de iniciar sesión')
        } else {
          setServerError(error)
        }
        return
      }

      if (user) {
        onSuccess?.()
      }
    } catch (error) {
      console.error('Login error:', error)
      setServerError('Error inesperado. Por favor, intenta nuevamente.')
    }
  }

  const isFormLoading = isLoading || isSubmitting

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={cn('space-y-4', className)}
    >
      {serverError && (
        <div className="rounded-md bg-destructive/10 border border-destructive/20 p-3">
          <p className="text-sm text-destructive">{serverError}</p>
        </div>
      )}

      <Input
        {...register('email')}
        type="email"
        label="Email"
        placeholder="<EMAIL>"
        error={errors.email?.message}
        disabled={isFormLoading}
        leftIcon={
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
            />
          </svg>
        }
        fullWidth
      />

      <Input
        {...register('password')}
        type="password"
        label="Contraseña"
        placeholder="••••••••"
        error={errors.password?.message}
        disabled={isFormLoading}
        leftIcon={
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        }
        fullWidth
      />

      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2 text-sm">
          <input
            {...register('rememberMe')}
            type="checkbox"
            className="rounded border-gray-300 text-primary focus:ring-primary"
            disabled={isFormLoading}
          />
          <span>Recordarme</span>
        </label>

        <button
          type="button"
          className="text-sm text-primary hover:underline"
          disabled={isFormLoading}
          onClick={() => {
            // TODO: Implement forgot password
            console.log('Forgot password clicked')
          }}
        >
          ¿Olvidaste tu contraseña?
        </button>
      </div>

      <Button
        type="submit"
        isLoading={isFormLoading}
        disabled={isFormLoading}
        fullWidth
      >
        {isFormLoading ? 'Iniciando sesión...' : 'Iniciar sesión'}
      </Button>

      {onToggleMode && (
        <div className="text-center text-sm">
          <span className="text-muted-foreground">¿No tienes una cuenta? </span>
          <button
            type="button"
            onClick={onToggleMode}
            className="text-primary hover:underline"
            disabled={isFormLoading}
          >
            Regístrate
          </button>
        </div>
      )}
    </form>
  )
}
