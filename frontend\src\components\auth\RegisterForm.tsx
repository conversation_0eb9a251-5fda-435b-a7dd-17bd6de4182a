import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { useAuth } from '@/hooks/useAuth'
import { registerSchema, type RegisterFormData } from '@/utils/validators'
import { cn } from '@/utils/cn'

interface RegisterFormProps {
  onToggleMode?: () => void
  onSuccess?: () => void
  className?: string
}

export const RegisterForm: React.FC<RegisterFormProps> = ({
  onToggleMode,
  onSuccess,
  className,
}) => {
  const { signUp, isLoading } = useAuth()
  const [serverError, setServerError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
      acceptTerms: false,
    },
  })

  // const password = watch('password') // For future password strength validation

  const onSubmit = async (data: RegisterFormData) => {
    try {
      setServerError(null)
      setSuccessMessage(null)
      
      const { user, error } = await signUp(data)
      
      if (error) {
        // Handle specific error types
        if (error.includes('User already registered')) {
          setError('email', { message: 'Este email ya está registrado' })
        } else if (error.includes('Password should be at least')) {
          setError('password', { message: 'La contraseña debe tener al menos 6 caracteres' })
        } else if (error.includes('Invalid email')) {
          setError('email', { message: 'Por favor, ingresa un email válido' })
        } else {
          setServerError(error)
        }
        return
      }

      if (user) {
        // Check if email confirmation is required
        if (!user.email_confirmed_at) {
          setSuccessMessage(
            'Cuenta creada exitosamente. Por favor, revisa tu email para confirmar tu cuenta.'
          )
        } else {
          onSuccess?.()
        }
      }
    } catch (error) {
      console.error('Registration error:', error)
      setServerError('Error inesperado. Por favor, intenta nuevamente.')
    }
  }

  const isFormLoading = isLoading || isSubmitting

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={cn('space-y-4', className)}
    >
      {serverError && (
        <div className="rounded-md bg-destructive/10 border border-destructive/20 p-3">
          <p className="text-sm text-destructive">{serverError}</p>
        </div>
      )}

      {successMessage && (
        <div className="rounded-md bg-green-50 border border-green-200 p-3">
          <p className="text-sm text-green-800">{successMessage}</p>
        </div>
      )}

      <Input
        {...register('fullName')}
        type="text"
        label="Nombre completo (opcional)"
        placeholder="Tu nombre"
        error={errors.fullName?.message}
        disabled={isFormLoading}
        leftIcon={
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        }
        fullWidth
      />

      <Input
        {...register('email')}
        type="email"
        label="Email"
        placeholder="<EMAIL>"
        error={errors.email?.message}
        disabled={isFormLoading}
        leftIcon={
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
            />
          </svg>
        }
        fullWidth
      />

      <Input
        {...register('password')}
        type="password"
        label="Contraseña"
        placeholder="••••••••"
        error={errors.password?.message}
        disabled={isFormLoading}
        leftIcon={
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        }
        helperText="Mínimo 8 caracteres, una mayúscula, una minúscula y un número"
        fullWidth
      />

      <Input
        {...register('confirmPassword')}
        type="password"
        label="Confirmar contraseña"
        placeholder="••••••••"
        error={errors.confirmPassword?.message}
        disabled={isFormLoading}
        leftIcon={
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        }
        fullWidth
      />

      <div className="space-y-2">
        <label className="flex items-start space-x-2 text-sm">
          <input
            {...register('acceptTerms')}
            type="checkbox"
            className="mt-0.5 rounded border-gray-300 text-primary focus:ring-primary"
            disabled={isFormLoading}
          />
          <span>
            Acepto los{' '}
            <a
              href="/terms"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              Términos de Servicio
            </a>{' '}
            y la{' '}
            <a
              href="/privacy"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              Política de Privacidad
            </a>
          </span>
        </label>
        {errors.acceptTerms && (
          <p className="text-sm text-destructive">{errors.acceptTerms.message}</p>
        )}
      </div>

      <Button
        type="submit"
        isLoading={isFormLoading}
        disabled={isFormLoading}
        fullWidth
      >
        {isFormLoading ? 'Creando cuenta...' : 'Crear cuenta'}
      </Button>

      {onToggleMode && (
        <div className="text-center text-sm">
          <span className="text-muted-foreground">¿Ya tienes una cuenta? </span>
          <button
            type="button"
            onClick={onToggleMode}
            className="text-primary hover:underline"
            disabled={isFormLoading}
          >
            Inicia sesión
          </button>
        </div>
      )}
    </form>
  )
}
