import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render } from '@/test/test-utils'
import { AuthPage } from '@/pages/AuthPage'
import { useAuth } from '@/hooks/useAuth'

// Mock the useAuth hook
vi.mock('@/hooks/useAuth')

describe('AuthPage Integration', () => {
  const mockAuth = {
    user: null,
    session: null,
    isLoading: false,
    isAuthenticated: false,
    signUp: vi.fn(),
    signIn: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
    refreshSession: vi.fn(),
    hasRole: vi.fn(),
    hasPermission: vi.fn(),
    getDisplayName: vi.fn(() => 'Usuario'),
    getAvatarUrl: vi.fn(() => null),
    isSessionExpired: vi.fn(() => false),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockAuth.isLoading = false
    mockAuth.isAuthenticated = false
    ;(useAuth as any).mockReturnValue(mockAuth)
  })

  it('renders login form by default', () => {
    render(<AuthPage />)

    expect(screen.getByRole('heading', { name: 'Iniciar sesión' })).toBeInTheDocument()
    expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('••••••••')).toBeInTheDocument()
  })

  it('switches to register form when toggle is clicked', async () => {
    render(<AuthPage />)

    const registerLink = screen.getByText('Regístrate')
    fireEvent.click(registerLink)

    await waitFor(() => {
      expect(screen.getByRole('heading', { name: 'Crear cuenta' })).toBeInTheDocument()
      expect(screen.getByText('Únete a TradingIA y comienza a analizar el mercado')).toBeInTheDocument()
    })
  })

  it('renders login form elements', () => {
    render(<AuthPage />)

    // Check that form elements are present
    expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('••••••••')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Iniciar sesión' })).toBeInTheDocument()
  })

  it('shows loading state during authentication check', () => {
    mockAuth.isLoading = true
    ;(useAuth as any).mockReturnValue(mockAuth)

    render(<AuthPage />)

    expect(screen.getByText('Verificando autenticación...')).toBeInTheDocument()
  })

  it('renders correctly when not authenticated', () => {
    mockAuth.isAuthenticated = false
    mockAuth.isLoading = false
    ;(useAuth as any).mockReturnValue(mockAuth)

    render(<AuthPage />)

    expect(screen.getByRole('heading', { name: 'Iniciar sesión' })).toBeInTheDocument()
    expect(screen.getByText('Accede a tu cuenta de TradingIA')).toBeInTheDocument()
  })
})
