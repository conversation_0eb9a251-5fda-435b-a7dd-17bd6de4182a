import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { cn } from '@/utils/cn'
import { CHAT_CONFIG } from '@/utils/constants'

interface ChatInputProps {
  onSendMessage: (message: string) => void
  disabled?: boolean
  placeholder?: string
  maxLength?: number
  className?: string
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = 'Escribe tu mensaje...',
  maxLength = CHAT_CONFIG.MAX_MESSAGE_LENGTH,
  className,
}) => {
  const [message, setMessage] = useState('')
  const [isComposing, setIsComposing] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }, [message])

  // Focus textarea on mount
  useEffect(() => {
    if (textareaRef.current && !disabled) {
      textareaRef.current.focus()
    }
  }, [disabled])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    sendMessage()
  }

  const sendMessage = () => {
    const trimmedMessage = message.trim()
    if (!trimmedMessage || disabled || isComposing) return

    onSendMessage(trimmedMessage)
    setMessage('')
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Send on Enter (but not Shift+Enter)
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    if (value.length <= maxLength) {
      setMessage(value)
    }
  }

  const remainingChars = maxLength - message.length
  const isNearLimit = remainingChars <= 100
  const isAtLimit = remainingChars <= 0

  return (
    <div className={cn('border-t bg-background p-4', className)}>
      <form onSubmit={handleSubmit} className="space-y-2">
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={cn(
              'w-full resize-none rounded-lg border border-input bg-background px-4 py-3 pr-12',
              'text-sm placeholder:text-muted-foreground',
              'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
              'disabled:cursor-not-allowed disabled:opacity-50',
              'min-h-[44px] max-h-[120px]'
            )}
          />
          
          {/* Send button */}
          <Button
            type="submit"
            size="sm"
            disabled={disabled || !message.trim() || isAtLimit}
            className="absolute bottom-2 right-2 h-8 w-8 p-0"
            title="Enviar mensaje (Enter)"
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              />
            </svg>
          </Button>
        </div>

        {/* Character counter and help text */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            <span>Presiona Enter para enviar, Shift+Enter para nueva línea</span>
          </div>
          
          {isNearLimit && (
            <span className={cn(
              'font-medium',
              isAtLimit ? 'text-destructive' : 'text-warning'
            )}>
              {remainingChars} caracteres restantes
            </span>
          )}
        </div>
      </form>

      {/* Suggestions (optional) */}
      {message.length === 0 && !disabled && (
        <div className="mt-3 flex flex-wrap gap-2">
          {[
            '¿Cómo está el mercado hoy?',
            'Analiza AAPL',
            'Muestra el RSI de Bitcoin',
            '¿Qué acciones recomendarías?',
          ].map((suggestion) => (
            <Button
              key={suggestion}
              variant="outline"
              size="sm"
              onClick={() => setMessage(suggestion)}
              className="h-auto whitespace-normal p-2 text-xs"
            >
              {suggestion}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}
