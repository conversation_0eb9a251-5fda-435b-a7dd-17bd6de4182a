import React, { useEffect, useRef } from 'react'
import { MessageBubble } from './MessageBubble'
import { ChatInput } from './ChatInput'
import { TypingIndicator } from './TypingIndicator'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { useChat } from '@/hooks/useChat'
import { cn } from '@/utils/cn'

interface ChatWindowProps {
  className?: string
  showWelcomeMessage?: boolean
  maxHeight?: string
}

export const ChatWindow: React.FC<ChatWindowProps> = ({
  className,
  showWelcomeMessage = true,
  maxHeight = '600px',
}) => {
  const {
    messages,
    isLoading,
    isTyping,
    error,
    sendMessage,
    retryMessage,
    clearError,
    startNewConversation,
  } = useChat()

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages, isTyping])

  const handleSendMessage = async (messageText: string) => {
    try {
      await sendMessage(messageText)
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const handleRetryMessage = async (messageId: string) => {
    try {
      await retryMessage(messageId)
    } catch (error) {
      console.error('Error retrying message:', error)
    }
  }

  const handleCopyMessage = (content: string) => {
    // Optional: Show a toast notification
    console.log('Message copied:', content)
  }

  const hasMessages = messages.length > 0
  const showWelcome = showWelcomeMessage && !hasMessages

  return (
    <Card className={cn('flex h-full flex-col', className)} style={{ maxHeight }}>
      {/* Header */}
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
            <svg
              className="h-4 w-4 text-primary-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold">Asistente Financiero IA</h3>
            <p className="text-xs text-muted-foreground">
              {isTyping ? 'Escribiendo...' : 'En línea'}
            </p>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={startNewConversation}
          title="Nueva conversación"
        >
          <svg
            className="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        </Button>
      </div>

      {/* Messages area */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin"
      >
        {/* Welcome message */}
        {showWelcome && (
          <div className="text-center py-8">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
              <svg
                className="h-8 w-8 text-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">
              ¡Hola! Soy tu Asistente Financiero IA
            </h3>
            <p className="text-muted-foreground mb-4 max-w-md mx-auto">
              Puedo ayudarte a analizar el mercado, obtener datos de precios, 
              calcular indicadores técnicos y responder preguntas sobre finanzas.
            </p>
            <div className="text-sm text-muted-foreground">
              <p>Ejemplos de lo que puedes preguntar:</p>
              <ul className="mt-2 space-y-1">
                <li>• "¿Cómo está el precio de Tesla hoy?"</li>
                <li>• "Calcula el RSI de Bitcoin"</li>
                <li>• "Muestra el gráfico de Apple de la última semana"</li>
              </ul>
            </div>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="rounded-lg bg-destructive/10 border border-destructive/20 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <svg
                  className="h-4 w-4 text-destructive"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm text-destructive">{error}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="h-6 w-6 p-0"
              >
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        )}

        {/* Messages */}
        {messages.map((message, index) => (
          <MessageBubble
            key={message.id || `${message.role}-${index}`}
            message={message}
            isLast={index === messages.length - 1}
            onRetry={handleRetryMessage}
            onCopy={handleCopyMessage}
          />
        ))}

        {/* Typing indicator */}
        <TypingIndicator isVisible={isTyping} />

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Input area */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading}
        placeholder={
          hasMessages 
            ? 'Continúa la conversación...' 
            : 'Pregúntame sobre el mercado financiero...'
        }
      />
    </Card>
  )
}
