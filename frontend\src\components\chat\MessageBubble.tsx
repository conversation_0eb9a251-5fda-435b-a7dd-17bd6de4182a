import React, { useState } from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeSanitize from 'rehype-sanitize'
import { Button } from '@/components/ui/Button'
import { cn } from '@/utils/cn'
import { formatRelativeTime } from '@/utils/formatters'
import type { ChatMessage } from '@/types/chat'

interface MessageBubbleProps {
  message: ChatMessage
  isLast?: boolean
  onRetry?: (messageId: string) => void
  onCopy?: (content: string) => void
  className?: string
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isLast = false,
  onRetry,
  onCopy,
  className,
}) => {
  const [showActions, setShowActions] = useState(false)
  const [copied, setCopied] = useState(false)

  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const isSystem = message.role === 'system'
  const isFailed = message.status === 'failed'
  const isSending = message.status === 'sending'

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      setCopied(true)
      onCopy?.(message.content)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy message:', error)
    }
  }

  const handleRetry = () => {
    if (message.id && onRetry) {
      onRetry(message.id)
    }
  }

  // Don't render system messages in the UI
  if (isSystem) {
    return null
  }

  return (
    <div
      className={cn(
        'group flex w-full',
        isUser ? 'justify-end' : 'justify-start',
        isLast && 'mb-4',
        className
      )}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div
        className={cn(
          'relative max-w-[80%] rounded-lg px-4 py-2 shadow-sm',
          isUser
            ? 'bg-primary text-primary-foreground'
            : 'bg-muted text-muted-foreground',
          isFailed && 'border border-destructive bg-destructive/10',
          isSending && 'opacity-70'
        )}
      >
        {/* Message content */}
        <div className="space-y-2">
          {isAssistant ? (
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <ReactMarkdown
                rehypePlugins={[rehypeSanitize]}
                components={{
                // Customize markdown components for better styling
                p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                code: ({ children, className }) => {
                  const isInline = !className
                  return isInline ? (
                    <code className="rounded bg-muted px-1 py-0.5 text-sm font-mono">
                      {children}
                    </code>
                  ) : (
                    <pre className="overflow-x-auto rounded bg-muted p-2">
                      <code className="text-sm font-mono">{children}</code>
                    </pre>
                  )
                },
                ul: ({ children }) => <ul className="ml-4 list-disc">{children}</ul>,
                ol: ({ children }) => <ol className="ml-4 list-decimal">{children}</ol>,
                li: ({ children }) => <li className="mb-1">{children}</li>,
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-muted-foreground/20 pl-4 italic">
                    {children}
                  </blockquote>
                ),
              }}
                >
                {message.content}
              </ReactMarkdown>
            </div>
          ) : (
            <p className="whitespace-pre-wrap break-words">{message.content}</p>
          )}
        </div>

        {/* Status indicators */}
        <div className="mt-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Timestamp */}
            <span className="text-xs opacity-70">
              {formatRelativeTime(message.timestamp)}
            </span>

            {/* Status indicator */}
            {isSending && (
              <div className="flex items-center space-x-1">
                <div className="h-1 w-1 animate-pulse rounded-full bg-current" />
                <span className="text-xs">Enviando...</span>
              </div>
            )}

            {isFailed && (
              <div className="flex items-center space-x-1">
                <svg className="h-3 w-3 text-destructive" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-xs text-destructive">Error</span>
              </div>
            )}
          </div>
        </div>

        {/* Action buttons */}
        {(showActions || isFailed) && (
          <div className="absolute -right-2 -top-2 flex space-x-1">
            {/* Copy button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              title={copied ? 'Copiado!' : 'Copiar mensaje'}
            >
              {copied ? (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              )}
            </Button>

            {/* Retry button for failed messages */}
            {isFailed && isUser && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRetry}
                className="h-6 w-6 p-0"
                title="Reintentar envío"
              >
                <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
