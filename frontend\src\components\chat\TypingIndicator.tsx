import React from 'react'
import { cn } from '@/utils/cn'

interface TypingIndicatorProps {
  isVisible: boolean
  text?: string
  className?: string
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  isVisible,
  text = 'IA está escribiendo',
  className,
}) => {
  if (!isVisible) return null

  return (
    <div className={cn('flex items-center space-x-2 p-4', className)}>
      <div className="flex items-center space-x-1">
        <div className="flex space-x-1">
          <div 
            className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"
            style={{ animationDelay: '0ms', animationDuration: '1.4s' }}
          />
          <div 
            className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"
            style={{ animationDelay: '200ms', animationDuration: '1.4s' }}
          />
          <div 
            className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"
            style={{ animationDelay: '400ms', animationDuration: '1.4s' }}
          />
        </div>
        <span className="text-sm text-muted-foreground ml-2">{text}</span>
      </div>
    </div>
  )
}
