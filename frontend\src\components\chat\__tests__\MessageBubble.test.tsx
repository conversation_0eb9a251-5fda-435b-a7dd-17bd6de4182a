import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { MessageBubble } from '../MessageBubble'
import type { ChatMessage } from '@/types/chat'

// Mock react-markdown
vi.mock('react-markdown', () => ({
  default: ({ children }: { children: string }) => <div data-testid="markdown">{children}</div>,
}))

// Mock rehype-sanitize
vi.mock('rehype-sanitize', () => ({
  default: () => {},
}))

describe('MessageBubble', () => {
  const mockUserMessage: ChatMessage = {
    id: '1',
    role: 'user',
    content: 'Hello, this is a user message',
    timestamp: '2023-01-01T12:00:00Z',
    status: 'sent',
  }

  const mockAssistantMessage: ChatMessage = {
    id: '2',
    role: 'assistant',
    content: 'Hello! This is an assistant response with **markdown**.',
    timestamp: '2023-01-01T12:01:00Z',
    status: 'delivered',
  }

  it('renders user message correctly', () => {
    render(<MessageBubble message={mockUserMessage} />)

    expect(screen.getByText('Hello, this is a user message')).toBeInTheDocument()
    expect(screen.getByText('1 ene 2023')).toBeInTheDocument() // Formatted time
  })

  it('renders assistant message with markdown', () => {
    render(<MessageBubble message={mockAssistantMessage} />)
    
    expect(screen.getByTestId('markdown')).toBeInTheDocument()
    expect(screen.getByText('Hello! This is an assistant response with **markdown**.')).toBeInTheDocument()
  })

  it('shows different styles for user vs assistant', () => {
    const { rerender } = render(<MessageBubble message={mockUserMessage} />)

    // Check user message exists
    expect(screen.getByText('Hello, this is a user message')).toBeInTheDocument()

    rerender(<MessageBubble message={mockAssistantMessage} />)

    // Check assistant message with markdown
    expect(screen.getByTestId('markdown')).toBeInTheDocument()
    expect(screen.getByText('Hello! This is an assistant response with **markdown**.')).toBeInTheDocument()
  })

  it('shows sending status', () => {
    const sendingMessage: ChatMessage = {
      ...mockUserMessage,
      status: 'sending',
    }

    render(<MessageBubble message={sendingMessage} />)
    expect(screen.getByText('Enviando...')).toBeInTheDocument()
  })

  it('shows failed status with retry option', () => {
    const failedMessage: ChatMessage = {
      ...mockUserMessage,
      status: 'failed',
    }

    const onRetry = vi.fn()
    render(<MessageBubble message={failedMessage} onRetry={onRetry} />)
    
    expect(screen.getByText('Error')).toBeInTheDocument()
    
    const retryButton = screen.getByTitle('Reintentar envío')
    expect(retryButton).toBeInTheDocument()
    
    fireEvent.click(retryButton)
    expect(onRetry).toHaveBeenCalledWith('1')
  })

  it('renders message content correctly', () => {
    render(<MessageBubble message={mockUserMessage} />)

    // Verify the message content is rendered
    expect(screen.getByText('Hello, this is a user message')).toBeInTheDocument()
    expect(screen.getByText('1 ene 2023')).toBeInTheDocument()

    // Verify the message container exists
    const messageContainer = screen.getByText('Hello, this is a user message').closest('div')
    expect(messageContainer).toBeInTheDocument()
  })

  it('does not render system messages', () => {
    const systemMessage: ChatMessage = {
      id: '3',
      role: 'system',
      content: 'This is a system message',
      timestamp: '2023-01-01T12:00:00Z',
    }

    const { container } = render(<MessageBubble message={systemMessage} />)
    expect(container.firstChild).toBeNull()
  })

  it('applies correct classes for failed messages', () => {
    const failedMessage: ChatMessage = {
      ...mockUserMessage,
      status: 'failed',
    }

    render(<MessageBubble message={failedMessage} />)
    expect(screen.getByText('Error')).toBeInTheDocument()
    expect(screen.getByTitle('Reintentar envío')).toBeInTheDocument()
  })

  it('shows sending status correctly', () => {
    const sendingMessage: ChatMessage = {
      ...mockUserMessage,
      status: 'sending',
    }

    render(<MessageBubble message={sendingMessage} />)
    expect(screen.getByText('Enviando...')).toBeInTheDocument()
    expect(screen.getByText('Hello, this is a user message')).toBeInTheDocument()
  })
})
