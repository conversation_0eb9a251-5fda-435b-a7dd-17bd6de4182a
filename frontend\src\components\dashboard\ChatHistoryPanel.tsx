import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Spinner } from '@/components/ui/Spinner'
import { useChatHistory } from '@/hooks/useChatHistory'
import { useChatHistoryStore } from '@/store/chatStore'
import { cn } from '@/utils/cn'
import { formatRelativeTime, truncateText } from '@/utils/formatters'
import type { ConversationSummary } from '@/types/chat'

interface ChatHistoryPanelProps {
  className?: string
  onConversationSelect?: (conversationId: string) => void
}

export const ChatHistoryPanel: React.FC<ChatHistoryPanelProps> = ({
  className,
  onConversationSelect,
}) => {
  const {
    conversations,
    isLoading,
    isDeletingConversation,
    error,
    loadConversation,
    deleteConversation,
    refreshConversations,
  } = useChatHistory()

  const { selectedConversationId } = useChatHistoryStore()
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleConversationClick = async (conversation: ConversationSummary) => {
    try {
      await loadConversation(conversation.id)
      onConversationSelect?.(conversation.id)
    } catch (error) {
      console.error('Error loading conversation:', error)
    }
  }

  const handleDeleteConversation = async (
    e: React.MouseEvent,
    conversationId: string
  ) => {
    e.stopPropagation()
    
    if (!confirm('¿Estás seguro de que quieres eliminar esta conversación?')) {
      return
    }

    try {
      setDeletingId(conversationId)
      await deleteConversation(conversationId)
    } catch (error) {
      console.error('Error deleting conversation:', error)
    } finally {
      setDeletingId(null)
    }
  }

  const handleRefresh = () => {
    refreshConversations()
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Historial</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Spinner size="md" text="Cargando historial..." />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Historial</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="mb-4 text-destructive">
              <svg className="h-8 w-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              Reintentar
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg">Historial</CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          title="Actualizar historial"
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        </Button>
      </CardHeader>
      
      <CardContent className="p-0">
        {conversations.length === 0 ? (
          <div className="text-center py-8 px-4">
            <div className="mb-4 text-muted-foreground">
              <svg className="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <p className="text-sm text-muted-foreground">
              No hay conversaciones aún.
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Inicia una conversación para ver el historial aquí.
            </p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto scrollbar-thin">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                className={cn(
                  'group relative cursor-pointer border-b p-4 transition-colors hover:bg-muted/50',
                  selectedConversationId === conversation.id && 'bg-muted'
                )}
                onClick={() => handleConversationClick(conversation)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium truncate">
                      {conversation.title}
                    </h4>
                    
                    {conversation.preview && (
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                        {truncateText(conversation.preview, 80)}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-2 mt-2 text-xs text-muted-foreground">
                      <span>{formatRelativeTime(conversation.last_updated)}</span>
                      <span>•</span>
                      <span>{conversation.message_count} mensajes</span>
                    </div>
                  </div>

                  {/* Delete button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => handleDeleteConversation(e, conversation.id)}
                    disabled={deletingId === conversation.id || isDeletingConversation}
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Eliminar conversación"
                  >
                    {deletingId === conversation.id ? (
                      <Spinner size="sm" />
                    ) : (
                      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
