import React, { useEffect, useRef, useState } from 'react'
import { createC<PERSON>, type IChartApi } from 'lightweight-charts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { cn } from '@/utils/cn'
import type { ChartData } from '@/types'

interface MainChartProps {
  data?: ChartData
  className?: string
  height?: number
}

export const MainChart: React.FC<MainChartProps> = ({
  data,
  className,
  height = 400,
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const seriesRef = useRef<any>(null)
  const [chartType, setChartType] = useState<'candlestick' | 'line'>('candlestick')
  const [isLoading, setIsLoading] = useState(false)

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height,
      layout: {
        background: { color: 'transparent' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#e1e5e9' },
        horzLines: { color: '#e1e5e9' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#e1e5e9',
      },
      timeScale: {
        borderColor: '#e1e5e9',
        timeVisible: true,
        secondsVisible: false,
      },
    })

    chartRef.current = chart

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
      }
    }
  }, [height])

  // Update chart data when data changes
  useEffect(() => {
    if (!chartRef.current || !data || !data.data.length) return

    setIsLoading(true)

    try {
      // Remove existing series
      if (seriesRef.current) {
        chartRef.current.removeSeries(seriesRef.current)
        seriesRef.current = null
      }

      // Create new series based on chart type
      if (chartType === 'candlestick') {
        const candlestickSeries = (chartRef.current as any).addCandlestickSeries({
          upColor: '#26a69a',
          downColor: '#ef5350',
          borderVisible: false,
          wickUpColor: '#26a69a',
          wickDownColor: '#ef5350',
        })

        // Convert OHLCV data to candlestick format
        const candlestickData = data.data.map((item) => ({
          time: Math.floor(new Date(item.datetime).getTime() / 1000) as any,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
        }))

        candlestickSeries.setData(candlestickData)
        seriesRef.current = candlestickSeries
      } else {
        const lineSeries = (chartRef.current as any).addLineSeries({
          color: '#2962FF',
          lineWidth: 2,
        })

        // Convert OHLCV data to line format (using close prices)
        const lineData = data.data.map((item) => ({
          time: Math.floor(new Date(item.datetime).getTime() / 1000) as any,
          value: item.close,
        }))

        lineSeries.setData(lineData)
        seriesRef.current = lineSeries
      }

      // Fit content to show all data
      chartRef.current.timeScale().fitContent()
    } catch (error) {
      console.error('Error updating chart data:', error)
    } finally {
      setIsLoading(false)
    }
  }, [data, chartType])

  const handleChartTypeChange = (type: 'candlestick' | 'line') => {
    setChartType(type)
  }

  const hasData = data && data.data.length > 0

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-lg">
            {data?.symbol ? `Gráfico de ${data.symbol}` : 'Gráfico Financiero'}
          </CardTitle>
          {data?.interval && (
            <CardDescription>
              Intervalo: {data.interval}
            </CardDescription>
          )}
        </div>

        {hasData && (
          <div className="flex space-x-2">
            <Button
              variant={chartType === 'line' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleChartTypeChange('line')}
              disabled={isLoading}
            >
              Línea
            </Button>
            <Button
              variant={chartType === 'candlestick' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => handleChartTypeChange('candlestick')}
              disabled={isLoading}
            >
              Velas
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 z-10 flex items-center justify-center bg-background/80">
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                <span className="text-sm">Actualizando gráfico...</span>
              </div>
            </div>
          )}

          {!hasData ? (
            <div 
              className="flex items-center justify-center bg-muted rounded-md"
              style={{ height }}
            >
              <div className="text-center">
                <svg
                  className="h-12 w-12 mx-auto mb-4 text-muted-foreground"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
                <h3 className="text-lg font-medium mb-2">Sin datos para mostrar</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Pregunta a la IA por datos de precios para ver el gráfico
                </p>
                <div className="text-xs text-muted-foreground">
                  <p>Ejemplos:</p>
                  <ul className="mt-1 space-y-1">
                    <li>• "Muestra el gráfico de AAPL"</li>
                    <li>• "Precio de Bitcoin últimos 30 días"</li>
                    <li>• "Gráfico de Tesla semanal"</li>
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <div
              ref={chartContainerRef}
              style={{ height }}
              className="w-full"
            />
          )}
        </div>

        {hasData && (
          <div className="mt-4 flex items-center justify-between text-xs text-muted-foreground">
            <span>
              {data.data.length} puntos de datos
            </span>
            <span>
              Último: {new Date(data.data[data.data.length - 1]?.datetime).toLocaleString('es-ES')}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
