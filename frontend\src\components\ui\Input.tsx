import React from 'react'
import { cn } from '@/utils/cn'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  fullWidth?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = 'text',
      label,
      error,
      helperText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
    
    const baseClasses = [
      'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2',
      'text-sm file:border-0 file:bg-transparent',
      'file:text-sm file:font-medium placeholder:text-gray-500',
      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500',
      'focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
    ]

    const errorClasses = error
      ? 'border-red-500 focus-visible:ring-red-500'
      : ''

    const widthClasses = fullWidth ? 'w-full' : ''

    const inputClasses = cn(
      baseClasses,
      errorClasses,
      leftIcon && 'pl-10',
      rightIcon && 'pr-10',
      widthClasses,
      className
    )

    return (
      <div className={cn('space-y-2', fullWidth && 'w-full')}>
        {label && (
          <label
            htmlFor={inputId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500">
              {leftIcon}
            </div>
          )}

          <input
            type={type}
            className={inputClasses}
            ref={ref}
            id={inputId}
            {...props}
          />

          {rightIcon && (
            <div className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500">
              {rightIcon}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <p
            className={cn(
              'text-sm',
              error ? 'text-red-500' : 'text-gray-500'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export { Input }
