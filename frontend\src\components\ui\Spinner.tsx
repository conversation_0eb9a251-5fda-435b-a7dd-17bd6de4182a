import React from 'react'
import { cn } from '@/utils/cn'

export interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'primary' | 'secondary'
  text?: string
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size = 'md', variant = 'default', text, ...props }, ref) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-6 w-6',
      lg: 'h-8 w-8',
      xl: 'h-12 w-12',
    }

    const variantClasses = {
      default: 'text-gray-500',
      primary: 'text-blue-600',
      secondary: 'text-gray-900',
    }

    const spinnerClasses = cn(
      'animate-spin',
      sizeClasses[size],
      variantClasses[variant]
    )

    const containerClasses = cn(
      'flex items-center justify-center',
      text && 'flex-col space-y-2',
      className
    )

    return (
      <div ref={ref} className={containerClasses} {...props}>
        <svg
          className={spinnerClasses}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        {text && (
          <p className="text-sm text-muted-foreground">{text}</p>
        )}
      </div>
    )
  }
)

Spinner.displayName = 'Spinner'

// Loading overlay component
export interface LoadingOverlayProps {
  isLoading: boolean
  text?: string
  className?: string
  children: React.ReactNode
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  text = 'Cargando...',
  className,
  children,
}) => {
  return (
    <div className={cn('relative', className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm">
          <Spinner size="lg" text={text} />
        </div>
      )}
    </div>
  )
}

// Inline loading component
export interface InlineLoadingProps {
  text?: string
  size?: SpinnerProps['size']
  className?: string
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  text = 'Cargando...',
  size = 'sm',
  className,
}) => {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <Spinner size={size} />
      <span className="text-sm text-muted-foreground">{text}</span>
    </div>
  )
}

export { Spinner }
