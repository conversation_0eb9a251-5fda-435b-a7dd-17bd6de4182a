import { describe, it, expect, vi } from 'vitest'
import { useAuthStore } from '@/store/authStore'

// Mock the auth store
vi.mock('@/store/authStore')

describe('useAuth', () => {
  it('auth store is properly mocked', () => {
    // Mock the store implementation
    const mockStore = {
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,
      signUp: vi.fn(),
      signIn: vi.fn(),
      signOut: vi.fn(),
      resetPassword: vi.fn(),
      updatePassword: vi.fn(),
      setUser: vi.fn(),
      setSession: vi.fn(),
      setLoading: vi.fn(),
      clearAuth: vi.fn(),
    }

    ;(useAuthStore as any).mockReturnValue(mockStore)

    const store = useAuthStore()
    expect(store.user).toBe(null)
    expect(store.isAuthenticated).toBe(false)
    expect(typeof store.signIn).toBe('function')
    expect(typeof store.signOut).toBe('function')
  })

  it('auth functions are callable', () => {
    const mockStore = {
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,
      signUp: vi.fn(),
      signIn: vi.fn(),
      signOut: vi.fn(),
      resetPassword: vi.fn(),
      updatePassword: vi.fn(),
      setUser: vi.fn(),
      setSession: vi.fn(),
      setLoading: vi.fn(),
      clearAuth: vi.fn(),
    }

    ;(useAuthStore as any).mockReturnValue(mockStore)

    const store = useAuthStore()

    // Test that functions can be called
    store.signIn({ email: '<EMAIL>', password: 'password' })
    expect(mockStore.signIn).toHaveBeenCalledWith({ email: '<EMAIL>', password: 'password' })

    store.signOut()
    expect(mockStore.signOut).toHaveBeenCalled()
  })

  it('auth state can be updated', () => {
    const mockStore = {
      user: { id: '1', email: '<EMAIL>' },
      session: { access_token: 'token' },
      isLoading: false,
      isAuthenticated: true,
      signUp: vi.fn(),
      signIn: vi.fn(),
      signOut: vi.fn(),
      resetPassword: vi.fn(),
      updatePassword: vi.fn(),
      setUser: vi.fn(),
      setSession: vi.fn(),
      setLoading: vi.fn(),
      clearAuth: vi.fn(),
    }

    ;(useAuthStore as any).mockReturnValue(mockStore)

    const store = useAuthStore()
    expect(store.isAuthenticated).toBe(true)
    expect(store.user?.email).toBe('<EMAIL>')
  })
})
