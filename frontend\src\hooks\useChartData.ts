import { useState, useEffect } from 'react'
import { useChatStore } from '@/store/chatStore'
import type { ChartData, OHLCVData } from '@/types'

/**
 * Hook to extract and manage chart data from AI responses
 * Monitors chat messages for financial data and updates chart state
 */
export const useChartData = () => {
  const { messages } = useChatStore()
  const [chartData, setChartData] = useState<ChartData | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  // Extract chart data from AI messages
  useEffect(() => {
    const extractChartDataFromMessages = () => {
      setIsProcessing(true)

      try {
        // Look for the most recent assistant message that might contain chart data
        const assistantMessages = messages
          .filter(msg => msg.role === 'assistant')
          .reverse() // Start from most recent

        for (const message of assistantMessages) {
          const extractedData = extractChartDataFromText(message.content)
          if (extractedData) {
            setChartData(extractedData)
            break
          }
        }
      } catch (error) {
        console.error('Error extracting chart data:', error)
      } finally {
        setIsProcessing(false)
      }
    }

    // Only process if we have messages
    if (messages.length > 0) {
      extractChartDataFromMessages()
    }
  }, [messages])

  // Function to extract chart data from text content
  const extractChartDataFromText = (content: string): ChartData | null => {
    try {
      // Look for JSON data blocks in the content
      const jsonMatches = content.match(/```json\s*([\s\S]*?)\s*```/g)
      
      if (jsonMatches) {
        for (const match of jsonMatches) {
          const jsonContent = match.replace(/```json\s*|\s*```/g, '')
          try {
            const parsed = JSON.parse(jsonContent)
            
            // Check if it looks like OHLCV data
            if (isValidOHLCVData(parsed)) {
              return {
                type: 'candlestick',
                data: parsed.data || parsed,
                symbol: parsed.symbol,
                interval: parsed.interval,
              }
            }
          } catch (parseError) {
            continue // Try next match
          }
        }
      }

      // Look for table-like data patterns
      const tableData = extractTableData(content)
      if (tableData) {
        return tableData
      }

      // Look for specific patterns in the text
      const patternData = extractPatternData(content)
      if (patternData) {
        return patternData
      }

      return null
    } catch (error) {
      console.error('Error parsing chart data:', error)
      return null
    }
  }

  // Validate if data structure looks like OHLCV
  const isValidOHLCVData = (data: any): boolean => {
    if (!Array.isArray(data) && !data.data) return false
    
    const dataArray = Array.isArray(data) ? data : data.data
    if (!Array.isArray(dataArray) || dataArray.length === 0) return false

    // Check if first item has required OHLCV fields
    const firstItem = dataArray[0]
    return (
      firstItem &&
      typeof firstItem === 'object' &&
      ('datetime' in firstItem || 'time' in firstItem || 'date' in firstItem) &&
      ('open' in firstItem || 'close' in firstItem || 'price' in firstItem)
    )
  }

  // Extract data from markdown tables
  const extractTableData = (content: string): ChartData | null => {
    // Look for markdown tables with financial data
    const tableRegex = /\|[^|]*(?:Date|Time|DateTime)[^|]*\|[^|]*(?:Open|Price|Close)[^|]*\|[\s\S]*?\n(?:\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|\s*\n)+/gi
    const match = tableRegex.exec(content)
    
    if (!match) return null

    try {
      const tableText = match[0]
      const lines = tableText.split('\n').filter(line => line.trim() && !line.includes('---'))
      
      if (lines.length < 2) return null

      // Parse header
      const headers = lines[0].split('|').map(h => h.trim().toLowerCase()).filter(h => h)
      
      // Find column indices
      const dateIndex = headers.findIndex(h => h.includes('date') || h.includes('time'))
      const openIndex = headers.findIndex(h => h.includes('open'))
      const highIndex = headers.findIndex(h => h.includes('high'))
      const lowIndex = headers.findIndex(h => h.includes('low'))
      const closeIndex = headers.findIndex(h => h.includes('close') || h.includes('price'))
      const volumeIndex = headers.findIndex(h => h.includes('volume'))

      if (dateIndex === -1 || closeIndex === -1) return null

      // Parse data rows
      const data: OHLCVData[] = []
      
      for (let i = 1; i < lines.length; i++) {
        const cells = lines[i].split('|').map(c => c.trim()).filter(c => c)
        if (cells.length < headers.length) continue

        try {
          const datetime = cells[dateIndex]
          const open = openIndex !== -1 ? parseFloat(cells[openIndex]) : parseFloat(cells[closeIndex])
          const high = highIndex !== -1 ? parseFloat(cells[highIndex]) : open
          const low = lowIndex !== -1 ? parseFloat(cells[lowIndex]) : open
          const close = parseFloat(cells[closeIndex])
          const volume = volumeIndex !== -1 ? parseFloat(cells[volumeIndex]) : 0

          if (!isNaN(close)) {
            data.push({
              datetime,
              open: isNaN(open) ? close : open,
              high: isNaN(high) ? close : high,
              low: isNaN(low) ? close : low,
              close,
              volume: isNaN(volume) ? 0 : volume,
            })
          }
        } catch (error) {
          continue // Skip invalid rows
        }
      }

      if (data.length > 0) {
        return {
          type: openIndex !== -1 ? 'candlestick' : 'line',
          data,
        }
      }
    } catch (error) {
      console.error('Error parsing table data:', error)
    }

    return null
  }

  // Extract data from text patterns
  const extractPatternData = (content: string): ChartData | null => {
    // Look for patterns like "Price: $123.45 at 2023-01-01"
    const pricePattern = /(?:price|close|value):\s*\$?([0-9,]+\.?[0-9]*)\s*(?:at|on)?\s*([0-9]{4}-[0-9]{2}-[0-9]{2})/gi
    const matches = [...content.matchAll(pricePattern)]

    if (matches.length > 0) {
      const data: OHLCVData[] = matches.map(match => {
        const price = parseFloat(match[1].replace(/,/g, ''))
        const datetime = match[2]
        
        return {
          datetime,
          open: price,
          high: price,
          low: price,
          close: price,
          volume: 0,
        }
      })

      return {
        type: 'line',
        data,
      }
    }

    return null
  }

  // Function to manually set chart data
  const setManualChartData = (data: ChartData) => {
    setChartData(data)
  }

  // Function to clear chart data
  const clearChartData = () => {
    setChartData(null)
  }

  return {
    chartData,
    isProcessing,
    setManualChartData,
    clearChartData,
    hasData: chartData !== null && chartData.data.length > 0,
  }
}
