import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useChatStore } from '@/store/chatStore'
import { useAuth } from '@/hooks/useAuth'
import { chatApi } from '@/api'
import type { ChatMessage, ChatRequest } from '@/types/chat'
import { v4 as uuidv4 } from 'uuid'

/**
 * Custom hook for chat functionality with optimistic updates
 * Uses TanStack Query for server state management
 */
export const useChat = () => {
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const {
    messages,
    currentConversationId,
    isLoading,
    isTyping,
    error,
    addMessage,
    updateMessage,
    setLoading,
    setTyping,
    setError,
    setCurrentConversationId,
  } = useChatStore()

  // Mutation for sending messages with optimistic updates
  const sendMessageMutation = useMutation({
    mutationFn: async (messageText: string) => {
      if (!user) {
        throw new Error('Usuario no autenticado')
      }

      const request: ChatRequest = {
        message: messageText,
        history: messages.slice(-10), // Send last 10 messages for context
        conversation_id: currentConversationId || undefined,
      }

      return await chatApi.sendMessage(request)
    },
    
    onMutate: async (messageText: string) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['chat'] })

      // Create optimistic user message
      const userMessage: ChatMessage = {
        id: uuidv4(),
        role: 'user',
        content: messageText,
        timestamp: new Date().toISOString(),
        status: 'sending',
      }

      // Add user message immediately (optimistic update)
      addMessage(userMessage)
      setError(null)
      setTyping(true)

      return { userMessage }
    },

    onSuccess: (response, _messageText, context) => {
      // Update user message status to sent
      if (context?.userMessage) {
        updateMessage(context.userMessage.id!, {
          status: 'sent',
        })
      }

      // Add AI response
      const aiMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: response.reply,
        timestamp: response.timestamp,
        status: 'delivered',
        metadata: {
          conversationId: response.conversation_id,
          ...response.metadata,
        },
      }

      addMessage(aiMessage)

      // Update conversation ID if it's a new conversation
      if (response.conversation_id && response.conversation_id !== currentConversationId) {
        setCurrentConversationId(response.conversation_id)
      }

      setTyping(false)
      setError(null)

      // Invalidate chat history to refresh the conversations list
      queryClient.invalidateQueries({ queryKey: ['chatHistory'] })
    },

    onError: (error: any, _messageText, context) => {
      // Update user message status to failed
      if (context?.userMessage) {
        updateMessage(context.userMessage.id!, {
          status: 'failed',
        })
      }

      setTyping(false)
      setError(error.message || 'Error al enviar el mensaje')

      console.error('Error sending message:', error)
    },

    onSettled: () => {
      setLoading(false)
    },
  })

  // Function to send a message
  const sendMessage = async (messageText: string) => {
    if (!messageText.trim()) {
      setError('El mensaje no puede estar vacío')
      return
    }

    if (messageText.length > 2000) {
      setError('El mensaje es demasiado largo (máximo 2000 caracteres)')
      return
    }

    setLoading(true)
    await sendMessageMutation.mutateAsync(messageText)
  }

  // Function to retry a failed message
  const retryMessage = async (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId)
    if (!message || message.role !== 'user') {
      return
    }

    // Update message status to sending
    updateMessage(messageId, { status: 'sending' })
    
    try {
      await sendMessage(message.content)
    } catch (error) {
      updateMessage(messageId, { status: 'failed' })
    }
  }

  // Function to clear error
  const clearError = () => {
    setError(null)
  }

  // Function to start a new conversation
  const startNewConversation = () => {
    setCurrentConversationId(null)
    // Note: We don't clear messages here as they might want to keep the current chat
    // The user can manually clear if needed
  }

  return {
    // State
    messages,
    currentConversationId,
    isLoading: isLoading || sendMessageMutation.isPending,
    isTyping,
    error,
    
    // Actions
    sendMessage,
    retryMessage,
    clearError,
    startNewConversation,
    
    // Mutation state for additional control
    isSending: sendMessageMutation.isPending,
    sendError: sendMessageMutation.error,
  }
}
