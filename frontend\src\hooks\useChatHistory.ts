import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/supabaseClient'
import { useChatStore, useChatHistoryStore } from '@/store/chatStore'
import { useAuth } from '@/hooks/useAuth'
import type { ChatMessage, ConversationSummary } from '@/types/chat'

/**
 * Custom hook for chat history management with TanStack Query
 * Handles conversation list and individual conversation loading
 */
export const useChatHistory = () => {
  const queryClient = useQueryClient()
  const { user } = useAuth()
  const { setMessages, setCurrentConversationId } = useChatStore()
  const { setSelectedConversation } = useChatHistoryStore()

  // Query for fetching conversation summaries
  const conversationsQuery = useQuery({
    queryKey: ['chatHistory', user?.id],
    queryFn: async (): Promise<ConversationSummary[]> => {
      if (!user) {
        throw new Error('Usuario no autenticado')
      }

      const { data, error } = await supabase
        .from('chat_history')
        .select(`
          conversation_id,
          created_at,
          updated_at,
          request_messages,
          ai_response
        `)
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })

      if (error) {
        throw new Error(`Error al cargar el historial: ${error.message}`)
      }

      // Group by conversation_id and create summaries
      const conversationMap = new Map<string, any>()
      
      data?.forEach((record) => {
        const convId = record.conversation_id || 'default'
        
        if (!conversationMap.has(convId)) {
          conversationMap.set(convId, {
            id: convId,
            title: '', // Will be set below
            last_updated: record.updated_at,
            message_count: 0,
            preview: '',
            messages: [],
          })
        }

        const conversation = conversationMap.get(convId)
        conversation.message_count += 1
        
        // Update last_updated if this record is newer
        if (new Date(record.updated_at) > new Date(conversation.last_updated)) {
          conversation.last_updated = record.updated_at
        }

        // Add messages from this record
        if (record.request_messages && Array.isArray(record.request_messages)) {
          conversation.messages.push(...record.request_messages)
        }
        
        if (record.ai_response) {
          conversation.messages.push({
            role: 'assistant',
            content: record.ai_response,
            timestamp: record.updated_at,
          })
        }
      })

      // Convert to array and generate titles/previews
      const conversations: ConversationSummary[] = Array.from(conversationMap.values()).map((conv) => {
        // Generate title from first user message or use default
        const firstUserMessage = conv.messages.find((msg: any) => msg.role === 'user')
        const title = firstUserMessage 
          ? firstUserMessage.content.slice(0, 50) + (firstUserMessage.content.length > 50 ? '...' : '')
          : 'Nueva conversación'

        // Generate preview from last message
        const lastMessage = conv.messages[conv.messages.length - 1]
        const preview = lastMessage 
          ? lastMessage.content.slice(0, 100) + (lastMessage.content.length > 100 ? '...' : '')
          : ''

        return {
          id: conv.id,
          title,
          last_updated: conv.last_updated,
          message_count: Math.floor(conv.message_count / 2), // Approximate message pairs
          preview,
        }
      })

      return conversations
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })

  // Query for loading a specific conversation
  const loadConversationQuery = useQuery({
    queryKey: ['conversation', user?.id],
    queryFn: async (): Promise<ChatMessage[]> => {
      return [] // This will be triggered manually
    },
    enabled: false, // Only run when manually triggered
  })

  // Mutation for deleting a conversation
  const deleteConversationMutation = useMutation({
    mutationFn: async (conversationId: string) => {
      if (!user) {
        throw new Error('Usuario no autenticado')
      }

      const { error } = await supabase
        .from('chat_history')
        .delete()
        .eq('user_id', user.id)
        .eq('conversation_id', conversationId)

      if (error) {
        throw new Error(`Error al eliminar la conversación: ${error.message}`)
      }

      return conversationId
    },
    onSuccess: (deletedConversationId) => {
      // Invalidate conversations query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['chatHistory', user?.id] })
      
      // If the deleted conversation was selected, clear it
      const currentConversationId = useChatStore.getState().currentConversationId
      if (currentConversationId === deletedConversationId) {
        setMessages([])
        setCurrentConversationId(null)
        setSelectedConversation(null)
      }
    },
    onError: (error) => {
      console.error('Error deleting conversation:', error)
    },
  })

  // Function to load a specific conversation
  const loadConversation = async (conversationId: string) => {
    if (!user) {
      throw new Error('Usuario no autenticado')
    }

    try {
      const { data, error } = await supabase
        .from('chat_history')
        .select('request_messages, ai_response, created_at, updated_at')
        .eq('user_id', user.id)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (error) {
        throw new Error(`Error al cargar la conversación: ${error.message}`)
      }

      // Reconstruct the conversation messages
      const messages: ChatMessage[] = []
      
      data?.forEach((record) => {
        // Add user messages
        if (record.request_messages && Array.isArray(record.request_messages)) {
          record.request_messages.forEach((msg: any) => {
            if (msg.role && msg.content) {
              messages.push({
                id: `${record.created_at}-${msg.role}-${messages.length}`,
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp || record.created_at,
                status: 'delivered',
              })
            }
          })
        }

        // Add AI response
        if (record.ai_response) {
          messages.push({
            id: `${record.updated_at}-assistant-${messages.length}`,
            role: 'assistant',
            content: record.ai_response,
            timestamp: record.updated_at,
            status: 'delivered',
          })
        }
      })

      // Update chat store with loaded messages
      setMessages(messages)
      setCurrentConversationId(conversationId)
      setSelectedConversation(conversationId)

      return messages
    } catch (error) {
      console.error('Error loading conversation:', error)
      throw error
    }
  }

  // Function to delete a conversation
  const deleteConversation = async (conversationId: string) => {
    await deleteConversationMutation.mutateAsync(conversationId)
  }

  // Function to refresh conversations list
  const refreshConversations = () => {
    queryClient.invalidateQueries({ queryKey: ['chatHistory', user?.id] })
  }

  return {
    // Data
    conversations: conversationsQuery.data || [],
    
    // Loading states
    isLoading: conversationsQuery.isLoading,
    isLoadingConversation: loadConversationQuery.isFetching,
    isDeletingConversation: deleteConversationMutation.isPending,
    
    // Error states
    error: conversationsQuery.error?.message || null,
    deleteError: deleteConversationMutation.error?.message || null,
    
    // Actions
    loadConversation,
    deleteConversation,
    refreshConversations,
    
    // Query controls
    refetch: conversationsQuery.refetch,
  }
}
