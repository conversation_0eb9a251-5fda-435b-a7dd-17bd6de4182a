import { useState, useEffect, useCallback } from 'react'

/**
 * Custom hook for localStorage management with TypeScript support
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window === 'undefined') {
        return initialValue
      }
      
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  // Set value in localStorage and state
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value
        
        setStoredValue(valueToStore)
        
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore))
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error)
      }
    },
    [key, storedValue]
  )

  // Remove value from localStorage and reset to initial value
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue)
      
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key)
      }
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  }, [key, initialValue])

  // Listen for changes to this localStorage key from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue))
        } catch (error) {
          console.warn(`Error parsing localStorage value for key "${key}":`, error)
        }
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange)
      return () => window.removeEventListener('storage', handleStorageChange)
    }
  }, [key])

  return [storedValue, setValue, removeValue]
}

/**
 * Hook for managing user preferences in localStorage
 */
export function useUserPreferences() {
  const [preferences, setPreferences, removePreferences] = useLocalStorage('user-preferences', {
    theme: 'system' as 'light' | 'dark' | 'system',
    language: 'es',
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
    dashboard: {
      layout: 'default',
      chartType: 'candlestick',
      timeframe: '1D',
    },
  })

  const updateTheme = useCallback(
    (theme: 'light' | 'dark' | 'system') => {
      setPreferences(prev => ({ ...prev, theme }))
    },
    [setPreferences]
  )

  const updateLanguage = useCallback(
    (language: string) => {
      setPreferences(prev => ({ ...prev, language }))
    },
    [setPreferences]
  )

  const updateNotifications = useCallback(
    (notifications: Partial<typeof preferences.notifications>) => {
      setPreferences(prev => ({
        ...prev,
        notifications: { ...prev.notifications, ...notifications },
      }))
    },
    [setPreferences, preferences.notifications]
  )

  const updateDashboard = useCallback(
    (dashboard: Partial<typeof preferences.dashboard>) => {
      setPreferences(prev => ({
        ...prev,
        dashboard: { ...prev.dashboard, ...dashboard },
      }))
    },
    [setPreferences, preferences.dashboard]
  )

  return {
    preferences,
    updateTheme,
    updateLanguage,
    updateNotifications,
    updateDashboard,
    resetPreferences: removePreferences,
  }
}

/**
 * Hook for managing recently viewed items
 */
export function useRecentItems<T extends { id: string; title: string }>(
  key: string,
  maxItems: number = 10
) {
  const [recentItems, setRecentItems] = useLocalStorage<T[]>(key, [])

  const addRecentItem = useCallback(
    (item: T) => {
      setRecentItems(prev => {
        // Remove item if it already exists
        const filtered = prev.filter(existing => existing.id !== item.id)
        
        // Add to beginning and limit to maxItems
        return [item, ...filtered].slice(0, maxItems)
      })
    },
    [setRecentItems, maxItems]
  )

  const removeRecentItem = useCallback(
    (id: string) => {
      setRecentItems(prev => prev.filter(item => item.id !== id))
    },
    [setRecentItems]
  )

  const clearRecentItems = useCallback(() => {
    setRecentItems([])
  }, [setRecentItems])

  return {
    recentItems,
    addRecentItem,
    removeRecentItem,
    clearRecentItems,
  }
}
