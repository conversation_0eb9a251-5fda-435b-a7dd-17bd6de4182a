import { useState, useCallback } from 'react'
import { chatApi } from '@/api'

interface MarketAnalysisData {
  analysis: string
  timestamp: string
  status: string
}

interface BuyOpportunitiesData {
  opportunities: string
  timestamp: string
  status: string
}

interface FullDataReloadResult {
  message: string
  results: {
    cleaning_results: any
    basic_data: any
    advanced_analysis: any
    total_time_seconds: number
    start_time: string
    end_time: string
    status: string
    summary: {
      total_assets_processed: number
      total_indicators_calculated: number
      total_volume_analysis: number
      total_patterns_detected: number
      total_multi_timeframe: number
      total_support_resistance: number
      total_market_structure: number
      total_errors: number
      success_rate: number
      tables_cleaned: number
      records_deleted: number
    }
  }
  timestamp: string
  status: string
}

interface UseMarketAnalysisReturn {
  // Market Analysis
  marketAnalysis: MarketAnalysisData | null
  isLoadingAnalysis: boolean
  analysisError: string | null
  fetchMarketAnalysis: () => Promise<void>

  // Buy Opportunities
  buyOpportunities: BuyOpportunitiesData | null
  isLoadingOpportunities: boolean
  opportunitiesError: string | null
  fetchBuyOpportunities: () => Promise<void>

  // Scheduler functions
  schedulerStatus: string
  isLoadingScheduler: boolean
  schedulerError: string | null
  startScheduler: () => Promise<void>
  stopScheduler: () => Promise<void>
  getSchedulerStatus: () => Promise<void>
  triggerUpdate: (params?: { batch_size?: number; specific_symbols?: string }) => Promise<void>

  // Full Data Reload
  isLoadingFullReload: boolean
  fullReloadError: string | null
  triggerFullDataReload: () => Promise<FullDataReloadResult | null>

  // Combined actions
  refreshAll: () => Promise<void>
}

/**
 * Custom hook for market analysis and buy opportunities
 */
export const useMarketAnalysis = (): UseMarketAnalysisReturn => {
  // Market Analysis state
  const [marketAnalysis, setMarketAnalysis] = useState<MarketAnalysisData | null>(null)
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false)
  const [analysisError, setAnalysisError] = useState<string | null>(null)

  // Buy Opportunities state
  const [buyOpportunities, setBuyOpportunities] = useState<BuyOpportunitiesData | null>(null)
  const [isLoadingOpportunities, setIsLoadingOpportunities] = useState(false)
  const [opportunitiesError, setOpportunitiesError] = useState<string | null>(null)

  // Scheduler state
  const [schedulerStatus, setSchedulerStatus] = useState<string>('unknown')
  const [isLoadingScheduler, setIsLoadingScheduler] = useState(false)
  const [schedulerError, setSchedulerError] = useState<string | null>(null)

  // Full Data Reload state
  const [isLoadingFullReload, setIsLoadingFullReload] = useState(false)
  const [fullReloadError, setFullReloadError] = useState<string | null>(null)

  // Fetch market analysis
  const fetchMarketAnalysis = useCallback(async () => {
    setIsLoadingAnalysis(true)
    setAnalysisError(null)
    
    try {
      const data = await chatApi.getMarketAnalysis()
      setMarketAnalysis(data)
    } catch (error: any) {
      console.error('Error fetching market analysis:', error)
      setAnalysisError(error.message || 'Error al obtener análisis de mercado')
    } finally {
      setIsLoadingAnalysis(false)
    }
  }, [])

  // Fetch buy opportunities
  const fetchBuyOpportunities = useCallback(async () => {
    setIsLoadingOpportunities(true)
    setOpportunitiesError(null)
    
    try {
      const data = await chatApi.getBuyOpportunities()
      setBuyOpportunities(data)
    } catch (error: any) {
      console.error('Error fetching buy opportunities:', error)
      setOpportunitiesError(error.message || 'Error al obtener oportunidades de compra')
    } finally {
      setIsLoadingOpportunities(false)
    }
  }, [])

  // Start scheduler
  const startScheduler = useCallback(async () => {
    setIsLoadingScheduler(true)
    setSchedulerError(null)

    try {
      const data = await chatApi.startScheduler()
      setSchedulerStatus(data.result?.status || 'running')
      console.log('Scheduler started:', data)
    } catch (error: any) {
      console.error('Error starting scheduler:', error)
      setSchedulerError(error.message || 'Error al iniciar scheduler')
    } finally {
      setIsLoadingScheduler(false)
    }
  }, [])

  // Stop scheduler
  const stopScheduler = useCallback(async () => {
    setIsLoadingScheduler(true)
    setSchedulerError(null)

    try {
      const data = await chatApi.stopScheduler()
      setSchedulerStatus(data.result?.status || 'stopped')
      console.log('Scheduler stopped:', data)
    } catch (error: any) {
      console.error('Error stopping scheduler:', error)
      setSchedulerError(error.message || 'Error al detener scheduler')
    } finally {
      setIsLoadingScheduler(false)
    }
  }, [])

  // Get scheduler status
  const getSchedulerStatus = useCallback(async () => {
    try {
      const data = await chatApi.getSchedulerStatus()
      setSchedulerStatus(data.scheduler_status?.status || 'unknown')
    } catch (error: any) {
      console.error('Error getting scheduler status:', error)
      setSchedulerError(error.message || 'Error al obtener estado del scheduler')
    }
  }, [])

  // Trigger manual update
  const triggerUpdate = useCallback(async (params?: { batch_size?: number; specific_symbols?: string }) => {
    setIsLoadingAnalysis(true)
    setAnalysisError(null)

    try {
      const data = await chatApi.triggerManualUpdate(params)
      console.log('Manual update triggered:', data)
      // Refresh analysis after update
      await fetchMarketAnalysis()
    } catch (error: any) {
      console.error('Error triggering manual update:', error)
      setAnalysisError(error.message || 'Error al actualizar datos manualmente')
    } finally {
      setIsLoadingAnalysis(false)
    }
  }, [fetchMarketAnalysis])

  // Trigger full data reload
  const triggerFullDataReload = useCallback(async (): Promise<FullDataReloadResult | null> => {
    setIsLoadingFullReload(true)
    setFullReloadError(null)

    try {
      const result = await chatApi.triggerFullDataReload()

      // Refresh all data after successful reload
      await Promise.all([
        fetchMarketAnalysis(),
        fetchBuyOpportunities(),
        getSchedulerStatus()
      ])

      return result
    } catch (error: any) {
      console.error('Error triggering full data reload:', error)
      setFullReloadError(error.message || 'Error al ejecutar recarga completa')
      return null
    } finally {
      setIsLoadingFullReload(false)
    }
  }, [fetchMarketAnalysis, fetchBuyOpportunities, getSchedulerStatus])

  // Refresh all data
  const refreshAll = useCallback(async () => {
    await Promise.all([
      fetchMarketAnalysis(),
      fetchBuyOpportunities(),
      getSchedulerStatus()
    ])
  }, [fetchMarketAnalysis, fetchBuyOpportunities, getSchedulerStatus])

  return {
    // Market Analysis
    marketAnalysis,
    isLoadingAnalysis,
    analysisError,
    fetchMarketAnalysis,

    // Buy Opportunities
    buyOpportunities,
    isLoadingOpportunities,
    opportunitiesError,
    fetchBuyOpportunities,

    // Scheduler functions
    schedulerStatus,
    isLoadingScheduler,
    schedulerError,
    startScheduler,
    stopScheduler,
    getSchedulerStatus,
    triggerUpdate,

    // Full Data Reload
    isLoadingFullReload,
    fullReloadError,
    triggerFullDataReload,

    // Combined actions
    refreshAll,
  }
}
