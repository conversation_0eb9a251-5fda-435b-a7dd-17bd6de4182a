import { useState, useCallback, useEffect } from 'react'
import { chatApi } from '@/api'

interface SchedulerStatus {
  status: string
  last_run?: string
  next_run?: string
  total_runs: number
  error_count: number
  config: {
    enabled: boolean
    interval_minutes: number
    market_hours_only: boolean
  }
  thread_alive: boolean
}

interface UseSchedulerReturn {
  // Status
  schedulerStatus: SchedulerStatus | null
  isLoading: boolean
  error: string | null
  
  // Actions
  startScheduler: () => Promise<void>
  stopScheduler: () => Promise<void>
  refreshStatus: () => Promise<void>
  triggerUpdate: (params?: { batch_size?: number; specific_symbols?: string }) => Promise<void>
  
  // Computed values
  isRunning: boolean
  isStopped: boolean
  hasError: boolean
  nextRunFormatted: string | null
  lastRunFormatted: string | null
}

/**
 * Custom hook for scheduler management
 */
export const useScheduler = (): UseSchedulerReturn => {
  const [schedulerStatus, setSchedulerStatus] = useState<SchedulerStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Refresh scheduler status
  const refreshStatus = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await chatApi.getSchedulerStatus()
      setSchedulerStatus(response.scheduler_status)
    } catch (err: any) {
      console.error('Error fetching scheduler status:', err)
      setError(err.message || 'Error al obtener estado del scheduler')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Start scheduler
  const startScheduler = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await chatApi.startScheduler()
      
      if (response.status === 'success') {
        // Refresh status after starting
        await refreshStatus()
      } else {
        setError(response.result?.message || 'Error al iniciar scheduler')
      }
    } catch (err: any) {
      console.error('Error starting scheduler:', err)
      setError(err.message || 'Error al iniciar scheduler')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStatus])

  // Stop scheduler
  const stopScheduler = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await chatApi.stopScheduler()
      
      if (response.status === 'success') {
        // Refresh status after stopping
        await refreshStatus()
      } else {
        setError(response.result?.message || 'Error al detener scheduler')
      }
    } catch (err: any) {
      console.error('Error stopping scheduler:', err)
      setError(err.message || 'Error al detener scheduler')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStatus])

  // Trigger manual update
  const triggerUpdate = useCallback(async (params?: { batch_size?: number; specific_symbols?: string }) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await chatApi.triggerManualUpdate(params)
      
      if (response.status === 'success') {
        console.log('Manual update completed:', response.result)
        // Optionally refresh status
        await refreshStatus()
      } else {
        setError('Error en actualización manual')
      }
    } catch (err: any) {
      console.error('Error triggering manual update:', err)
      setError(err.message || 'Error en actualización manual')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStatus])

  // Auto-refresh status on mount and periodically
  useEffect(() => {
    refreshStatus()
    
    // Poll status every 30 seconds
    const interval = setInterval(refreshStatus, 30000)
    
    return () => clearInterval(interval)
  }, [refreshStatus])

  // Computed values
  const isRunning = schedulerStatus?.status === 'running'
  const isStopped = schedulerStatus?.status === 'stopped'
  const hasError = schedulerStatus?.status === 'error' || !!error

  const formatDateTime = (dateString?: string): string | null => {
    if (!dateString) return null
    
    try {
      const date = new Date(dateString)
      return date.toLocaleString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  const nextRunFormatted = formatDateTime(schedulerStatus?.next_run)
  const lastRunFormatted = formatDateTime(schedulerStatus?.last_run)

  return {
    // Status
    schedulerStatus,
    isLoading,
    error,
    
    // Actions
    startScheduler,
    stopScheduler,
    refreshStatus,
    triggerUpdate,
    
    // Computed values
    isRunning,
    isStopped,
    hasError,
    nextRunFormatted,
    lastRunFormatted,
  }
}
