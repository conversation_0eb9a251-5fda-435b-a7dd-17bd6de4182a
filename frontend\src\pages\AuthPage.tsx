import React, { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { AuthLayout } from '@/components/auth/AuthLayout'
import { LoginForm } from '@/components/auth/LoginForm'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { useAuth } from '@/hooks/useAuth'
import { ROUTES } from '@/utils/constants'

type AuthMode = 'login' | 'register'

export const AuthPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const { isAuthenticated, isLoading } = useAuth()
  
  // Get initial mode from URL params or default to login
  const initialMode = (searchParams.get('mode') as AuthMode) || 'login'
  const [mode, setMode] = useState<AuthMode>(initialMode)

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      const redirectTo = searchParams.get('redirect') || ROUTES.DASHBOARD
      navigate(redirectTo, { replace: true })
    }
  }, [isAuthenticated, isLoading, navigate, searchParams])

  // Update URL when mode changes
  useEffect(() => {
    const newSearchParams = new URLSearchParams(searchParams)
    newSearchParams.set('mode', mode)
    setSearchParams(newSearchParams, { replace: true })
  }, [mode, searchParams, setSearchParams])

  const handleToggleMode = () => {
    setMode(prev => prev === 'login' ? 'register' : 'login')
  }

  const handleAuthSuccess = () => {
    const redirectTo = searchParams.get('redirect') || ROUTES.DASHBOARD
    navigate(redirectTo, { replace: true })
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Verificando autenticación...</p>
        </div>
      </div>
    )
  }

  // Don't render if already authenticated (will redirect)
  if (isAuthenticated) {
    return null
  }

  const authConfig = {
    login: {
      title: 'Iniciar sesión',
      description: 'Accede a tu cuenta de TradingIA',
    },
    register: {
      title: 'Crear cuenta',
      description: 'Únete a TradingIA y comienza a analizar el mercado',
    },
  }

  const { title, description } = authConfig[mode]

  return (
    <AuthLayout title={title} description={description}>
      {mode === 'login' ? (
        <LoginForm
          onToggleMode={handleToggleMode}
          onSuccess={handleAuthSuccess}
        />
      ) : (
        <RegisterForm
          onToggleMode={handleToggleMode}
          onSuccess={handleAuthSuccess}
        />
      )}
    </AuthLayout>
  )
}
