import React from 'react'
import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { ChatHistoryPanel } from '@/components/dashboard/ChatHistoryPanel'
import { ChatWindow } from '@/components/chat/ChatWindow'
import { MainChart } from '@/components/dashboard/MainChart'
import { MarketAnalysisCard } from '@/components/dashboard/MarketAnalysisCard'
import { useChartData } from '@/hooks/useChartData'

export const DashboardPage: React.FC = () => {
  const { chartData } = useChartData()

  const handleConversationSelect = (conversationId: string) => {
    console.log('Selected conversation:', conversationId)
  }

  return (
    <DashboardLayout
      sidebar={
        <ChatHistoryPanel
          onConversationSelect={handleConversationSelect}
          className="h-full border-0"
        />
      }
    >
      <div className="flex h-full">
        {/* Main chat area */}
        <div className="flex-1 p-6">
          <ChatWindow className="h-full" />
        </div>

        {/* Right sidebar - Charts and Market Analysis */}
        <div className="w-96 border-l bg-card p-6 space-y-6 overflow-y-auto">
          {/* Charts */}
          <MainChart
            data={chartData || undefined}
            height={250}
          />

          {/* Market Analysis and Buy Opportunities */}
          <MarketAnalysisCard />
        </div>
      </div>
    </DashboardLayout>
  )
}
