import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { ChatStore, ChatMessage } from '@/types/chat'

// Create the chat store with persistence for current conversation
export const useChatStore = create<ChatStore>()(
  persist(
    (set, _get) => ({
      // State
      messages: [],
      currentConversationId: null,
      isLoading: false,
      isTyping: false,
      error: null,

      // Actions
      addMessage: (message: ChatMessage) => {
        set((state) => ({
          messages: [...state.messages, message],
          error: null, // Clear any previous errors when adding a message
        }))
      },

      updateMessage: (id: string, updates: Partial<ChatMessage>) => {
        set((state) => ({
          messages: state.messages.map((msg) =>
            msg.id === id ? { ...msg, ...updates } : msg
          ),
        }))
      },

      setMessages: (messages: ChatMessage[]) => {
        set({ messages, error: null })
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading })
      },

      setTyping: (isTyping: boolean) => {
        set({ isTyping })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      setCurrentConversationId: (currentConversationId: string | null) => {
        set({ currentConversationId })
      },

      clearChat: () => {
        set({
          messages: [],
          currentConversationId: null,
          isLoading: false,
          isTyping: false,
          error: null,
        })
      },

      removeMessage: (id: string) => {
        set((state) => ({
          messages: state.messages.filter((msg) => msg.id !== id),
        }))
      },
    }),
    {
      name: 'chat-storage',
      partialize: (state) => ({
        messages: state.messages,
        currentConversationId: state.currentConversationId,
      }),
    }
  )
)

// Create a separate store for chat history (conversations list)
export const useChatHistoryStore = create<{
  conversations: Array<{
    id: string
    title: string
    last_updated: string
    message_count: number
    preview?: string
  }>
  isLoading: boolean
  error: string | null
  selectedConversationId: string | null
  
  // Actions
  setConversations: (conversations: any[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setSelectedConversation: (id: string | null) => void
  addConversation: (conversation: any) => void
  updateConversation: (id: string, updates: any) => void
  removeConversation: (id: string) => void
  clearHistory: () => void
}>()(
  persist(
    (set) => ({
      // State
      conversations: [],
      isLoading: false,
      error: null,
      selectedConversationId: null,

      // Actions
      setConversations: (conversations) => {
        set({ conversations, error: null })
      },

      setLoading: (isLoading) => {
        set({ isLoading })
      },

      setError: (error) => {
        set({ error })
      },

      setSelectedConversation: (selectedConversationId) => {
        set({ selectedConversationId })
      },

      addConversation: (conversation) => {
        set((state) => ({
          conversations: [conversation, ...state.conversations],
        }))
      },

      updateConversation: (id, updates) => {
        set((state) => ({
          conversations: state.conversations.map((conv) =>
            conv.id === id ? { ...conv, ...updates } : conv
          ),
        }))
      },

      removeConversation: (id) => {
        set((state) => ({
          conversations: state.conversations.filter((conv) => conv.id !== id),
          selectedConversationId: 
            state.selectedConversationId === id ? null : state.selectedConversationId,
        }))
      },

      clearHistory: () => {
        set({
          conversations: [],
          selectedConversationId: null,
          error: null,
        })
      },
    }),
    {
      name: 'chat-history-storage',
      partialize: (state) => ({
        conversations: state.conversations,
        selectedConversationId: state.selectedConversationId,
      }),
    }
  )
)
