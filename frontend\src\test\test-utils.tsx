import React from 'react'
import { render, type RenderOptions } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Mock auth store
export const createMockAuthStore = () => ({
  user: null,
  session: null,
  isLoading: false,
  isAuthenticated: false,
  signUp: vi.fn(),
  signIn: vi.fn(),
  signOut: vi.fn(),
  resetPassword: vi.fn(),
  updatePassword: vi.fn(),
  setUser: vi.fn(),
  setSession: vi.fn(),
  setLoading: vi.fn(),
  clearAuth: vi.fn(),
})

// Mock chat store
export const createMockChatStore = () => ({
  messages: [],
  isLoading: false,
  error: null,
  sendMessage: vi.fn(),
  clearMessages: vi.fn(),
  setLoading: vi.fn(),
  setError: vi.fn(),
  addMessage: vi.fn(),
  updateMessage: vi.fn(),
})

// Mock chart data
export const mockChartData = {
  data: [
    {
      datetime: '2023-01-01T00:00:00Z',
      open: 100,
      high: 110,
      low: 95,
      close: 105,
      volume: 1000,
    },
    {
      datetime: '2023-01-02T00:00:00Z',
      open: 105,
      high: 115,
      low: 100,
      close: 110,
      volume: 1200,
    },
  ],
  symbol: 'NASDAQ:TSLA',
  interval: '1D',
}

// Mock API responses
export const mockApiResponses = {
  chat: {
    success: {
      response: 'This is a test response from the AI',
      conversation_id: 'test-conversation-id',
    },
    error: {
      detail: 'Something went wrong',
    },
  },
  priceData: mockChartData,
}

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }
