import { type User as <PERSON>pabaseUser, type Session as SupabaseSession } from '@supabase/supabase-js'

// Extended user type based on Supabase User
export interface User extends Omit<SupabaseUser, 'user_metadata'> {
  id: string
  email?: string
  created_at: string
  user_metadata?: {
    full_name?: string
    avatar_url?: string
    [key: string]: any
  }
}

// Session type from Supabase
export type Session = SupabaseSession

// Authentication forms
export interface LoginFormData {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterFormData {
  email: string
  password: string
  confirmPassword: string
  fullName?: string
  acceptTerms: boolean
}

export interface ForgotPasswordFormData {
  email: string
}

export interface ResetPasswordFormData {
  password: string
  confirmPassword: string
}

// Authentication state
export interface AuthState {
  user: User | null
  session: Session | null
  isLoading: boolean
  isAuthenticated: boolean
}

// Authentication actions
export interface AuthActions {
  signUp: (data: RegisterFormData) => Promise<{ user: User | null; error: string | null }>
  signIn: (data: LoginFormData) => Promise<{ user: User | null; error: string | null }>
  signOut: () => Promise<{ error: string | null }>
  resetPassword: (email: string) => Promise<{ error: string | null }>
  updatePassword: (password: string) => Promise<{ error: string | null }>
  setUser: (user: User | null) => void
  setSession: (session: Session | null) => void
  setLoading: (loading: boolean) => void
  clearAuth: () => void
}

// Combined auth store type
export interface AuthStore extends AuthState, AuthActions {}

// Authentication errors
export interface AuthError {
  message: string
  code?: string
  field?: keyof LoginFormData | keyof RegisterFormData
}

// Route protection
export interface ProtectedRouteProps {
  children: React.ReactNode
  redirectTo?: string
  requireAuth?: boolean
}
