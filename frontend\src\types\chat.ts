// Message types - aligned with backend Pydantic models
export interface ChatMessage {
  id?: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  status?: 'sending' | 'sent' | 'failed' | 'delivered'
  metadata?: {
    conversationId?: string
    toolCalls?: any[]
    [key: string]: any
  }
}

// Conversation types
export interface Conversation {
  id: string
  title: string
  messages: ChatMessage[]
  created_at: string
  updated_at: string
  user_id: string
  metadata?: {
    messageCount?: number
    lastActivity?: string
    [key: string]: any
  }
}

export interface ConversationSummary {
  id: string
  title: string
  last_updated: string
  message_count: number
  preview?: string
}

// API Request/Response types - aligned with backend
export interface ChatRequest {
  message: string
  history: ChatMessage[]
  conversation_id?: string
}

export interface ChatResponse {
  reply: string
  timestamp: string
  conversation_id: string
  metadata?: {
    toolsUsed?: string[]
    processingTime?: number
    [key: string]: any
  }
}

// Chat state management
export interface ChatState {
  messages: ChatMessage[]
  currentConversationId: string | null
  isLoading: boolean
  isTyping: boolean
  error: string | null
}

export interface ChatActions {
  addMessage: (message: ChatMessage) => void
  updateMessage: (id: string, updates: Partial<ChatMessage>) => void
  setMessages: (messages: ChatMessage[]) => void
  setLoading: (loading: boolean) => void
  setTyping: (typing: boolean) => void
  setError: (error: string | null) => void
  setCurrentConversationId: (id: string | null) => void
  clearChat: () => void
  removeMessage: (id: string) => void
}

// Combined chat store type
export interface ChatStore extends ChatState, ChatActions {}

// Chat history management
export interface ChatHistoryState {
  conversations: ConversationSummary[]
  isLoading: boolean
  error: string | null
  selectedConversationId: string | null
}

export interface ChatHistoryActions {
  fetchConversations: () => Promise<void>
  loadConversation: (id: string) => Promise<void>
  deleteConversation: (id: string) => Promise<void>
  createNewConversation: () => void
  setSelectedConversation: (id: string | null) => void
  setConversations: (conversations: ConversationSummary[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// Combined chat history store type
export interface ChatHistoryStore extends ChatHistoryState, ChatHistoryActions {}

// Chat input component props
export interface ChatInputProps {
  onSendMessage: (message: string) => void
  disabled?: boolean
  placeholder?: string
  maxLength?: number
}

// Message bubble component props
export interface MessageBubbleProps {
  message: ChatMessage
  isLast?: boolean
  onRetry?: (messageId: string) => void
  onCopy?: (content: string) => void
}

// Typing indicator props
export interface TypingIndicatorProps {
  isVisible: boolean
  text?: string
}

// Chat window props
export interface ChatWindowProps {
  className?: string
  showWelcomeMessage?: boolean
  maxHeight?: string
}
