// Re-export all types from individual modules
export * from './auth'
export * from './chat'

// Common utility types
export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  count: number
  page: number
  totalPages: number
}

// Loading states
export interface LoadingState {
  isLoading: boolean
  error: string | null
}

// Environment variables
export interface EnvConfig {
  VITE_SUPABASE_URL: string
  VITE_SUPABASE_ANON_KEY: string
  VITE_API_BASE_URL: string
  VITE_ENVIRONMENT: 'development' | 'production' | 'test'
}

// Chart data types for financial visualization
export interface OHLCVData {
  datetime: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface ChartData {
  type: 'line' | 'candlestick' | 'area'
  data: OHLCVData[]
  symbol?: string
  interval?: string
}

// API Error types
export interface ApiError {
  message: string
  code?: string
  details?: Record<string, any>
}

// Form validation types
export interface ValidationError {
  field: string
  message: string
}

export interface FormState<T> {
  data: T
  errors: ValidationError[]
  isSubmitting: boolean
  isDirty: boolean
}
