/**
 * Utility functions for formatting data
 */

// Date formatting
export const formatDate = (date: string | Date, options?: Intl.DateTimeFormatOptions): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options,
  }
  
  return dateObj.toLocaleDateString('es-ES', defaultOptions)
}

export const formatDateTime = (date: string | Date): string => {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

export const formatTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleTimeString('es-ES', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

export const formatRelativeTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return 'Hace un momento'
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `Hace ${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`
  }
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`
  }
  
  return formatDate(dateObj)
}

// Number formatting
export const formatNumber = (
  value: number,
  options?: Intl.NumberFormatOptions
): string => {
  const defaultOptions: Intl.NumberFormatOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  }
  
  return value.toLocaleString('es-ES', defaultOptions)
}

export const formatCurrency = (
  value: number,
  currency: string = 'USD',
  options?: Intl.NumberFormatOptions
): string => {
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    ...options,
  }
  
  return value.toLocaleString('es-ES', defaultOptions)
}

export const formatPercentage = (
  value: number,
  options?: Intl.NumberFormatOptions
): string => {
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    ...options,
  }
  
  return (value / 100).toLocaleString('es-ES', defaultOptions)
}

export const formatCompactNumber = (value: number): string => {
  const formatter = new Intl.NumberFormat('es-ES', {
    notation: 'compact',
    compactDisplay: 'short',
  })
  
  return formatter.format(value)
}

// String formatting
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength - 3) + '...'
}

export const capitalizeFirst = (text: string): string => {
  if (!text) return text
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
}

export const formatInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

// Financial data formatting
export const formatPrice = (price: number, decimals: number = 2): string => {
  return formatNumber(price, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  })
}

export const formatVolume = (volume: number): string => {
  if (volume >= 1e9) {
    return `${formatNumber(volume / 1e9, { maximumFractionDigits: 1 })}B`
  }
  if (volume >= 1e6) {
    return `${formatNumber(volume / 1e6, { maximumFractionDigits: 1 })}M`
  }
  if (volume >= 1e3) {
    return `${formatNumber(volume / 1e3, { maximumFractionDigits: 1 })}K`
  }
  return formatNumber(volume)
}

export const formatPriceChange = (change: number, isPercentage: boolean = false): string => {
  const sign = change >= 0 ? '+' : ''
  const formatted = isPercentage 
    ? formatPercentage(Math.abs(change))
    : formatNumber(Math.abs(change))
  
  return `${sign}${change >= 0 ? '' : '-'}${formatted}`
}

// File size formatting
export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Bytes'
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  const size = bytes / Math.pow(1024, i)
  
  return `${formatNumber(size, { maximumFractionDigits: 1 })} ${sizes[i]}`
}

// URL formatting
export const formatUrl = (url: string): string => {
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }
  return url
}

// Phone number formatting (basic)
export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '')
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/)
  
  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`
  }
  
  return phone
}
