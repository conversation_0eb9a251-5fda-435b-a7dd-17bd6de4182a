import { z } from 'zod'
import { VALIDATION_RULES } from './constants'

// Basic validation functions
export const isValidEmail = (email: string): boolean => {
  return VALIDATION_RULES.EMAIL.PATTERN.test(email)
}

export const isValidPassword = (password: string): boolean => {
  return (
    password.length >= VALIDATION_RULES.PASSWORD.MIN_LENGTH &&
    VALIDATION_RULES.PASSWORD.PATTERN.test(password)
  )
}

export const isValidName = (name: string): boolean => {
  return (
    name.length >= VALIDATION_RULES.NAME.MIN_LENGTH &&
    name.length <= VALIDATION_RULES.NAME.MAX_LENGTH &&
    VALIDATION_RULES.NAME.PATTERN.test(name)
  )
}

// Zod schemas for form validation
export const emailSchema = z
  .string()
  .min(1, 'El email es requerido')
  .email('Por favor, ingresa un email válido')

export const passwordSchema = z
  .string()
  .min(VALIDATION_RULES.PASSWORD.MIN_LENGTH, `La contraseña debe tener al menos ${VALIDATION_RULES.PASSWORD.MIN_LENGTH} caracteres`)
  .regex(
    VALIDATION_RULES.PASSWORD.PATTERN,
    'La contraseña debe contener al menos una mayúscula, una minúscula y un número'
  )

export const nameSchema = z
  .string()
  .min(VALIDATION_RULES.NAME.MIN_LENGTH, `El nombre debe tener al menos ${VALIDATION_RULES.NAME.MIN_LENGTH} caracteres`)
  .max(VALIDATION_RULES.NAME.MAX_LENGTH, `El nombre no puede tener más de ${VALIDATION_RULES.NAME.MAX_LENGTH} caracteres`)
  .regex(VALIDATION_RULES.NAME.PATTERN, 'El nombre solo puede contener letras y espacios')

// Authentication form schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'La contraseña es requerida'),
  rememberMe: z.boolean().optional(),
})

export const registerSchema = z
  .object({
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Confirma tu contraseña'),
    fullName: nameSchema.optional(),
    acceptTerms: z.boolean().refine(val => val === true, {
      message: 'Debes aceptar los términos y condiciones',
    }),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword'],
  })

export const forgotPasswordSchema = z.object({
  email: emailSchema,
})

export const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Confirma tu contraseña'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword'],
  })

// Chat form schemas
export const chatMessageSchema = z.object({
  message: z
    .string()
    .min(1, 'El mensaje no puede estar vacío')
    .max(2000, 'El mensaje no puede tener más de 2000 caracteres'),
})

// Profile form schemas
export const profileSchema = z.object({
  fullName: nameSchema,
  email: emailSchema,
  bio: z.string().max(500, 'La biografía no puede tener más de 500 caracteres').optional(),
})

export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'La contraseña actual es requerida'),
    newPassword: passwordSchema,
    confirmNewPassword: z.string().min(1, 'Confirma tu nueva contraseña'),
  })
  .refine(data => data.newPassword === data.confirmNewPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmNewPassword'],
  })

// File validation
export const validateImageFile = (file: File): { isValid: boolean; error?: string } => {
  const maxSize = 5 * 1024 * 1024 // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Solo se permiten archivos JPEG, PNG y WebP',
    }
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'El archivo no puede ser mayor a 5MB',
    }
  }

  return { isValid: true }
}

// URL validation
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Phone number validation (basic)
export const isValidPhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

// Credit card validation (basic Luhn algorithm)
export const isValidCreditCard = (cardNumber: string): boolean => {
  const cleaned = cardNumber.replace(/\D/g, '')
  
  if (cleaned.length < 13 || cleaned.length > 19) {
    return false
  }

  let sum = 0
  let isEven = false

  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i])

    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }

    sum += digit
    isEven = !isEven
  }

  return sum % 10 === 0
}

// Date validation
export const isValidDate = (date: string): boolean => {
  const parsedDate = new Date(date)
  return !isNaN(parsedDate.getTime())
}

export const isDateInFuture = (date: string): boolean => {
  const parsedDate = new Date(date)
  return parsedDate > new Date()
}

export const isDateInPast = (date: string): boolean => {
  const parsedDate = new Date(date)
  return parsedDate < new Date()
}

// Age validation
export const calculateAge = (birthDate: string): number => {
  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

export const isValidAge = (birthDate: string, minAge: number = 18): boolean => {
  const age = calculateAge(birthDate)
  return age >= minAge
}

// Export types for TypeScript
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>
export type ChatMessageFormData = z.infer<typeof chatMessageSchema>
export type ProfileFormData = z.infer<typeof profileSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>
